# -*- mode: python ; coding: utf-8 -*-
"""
ملف تكوين PyInstaller لنظام إدارة الموظفين المبسط
"""

import os
from PyInstaller.utils.hooks import collect_data_files

# جمع ملفات البيانات المطلوبة
data_files = []

# إضافة ملفات البيانات
if os.path.exists('data'):
    data_files.append(('data', 'data'))

# إضافة ملفات الإعدادات
config_files = [
    'employee_config.json',
    'font_settings.json', 
    'view_preferences.json',
    'users.json',
    'current_session.json'
]

for config_file in config_files:
    if os.path.exists(config_file):
        data_files.append((config_file, '.'))

# إضافة ملف البيانات المرجعية
if os.path.exists('reference_data.py'):
    data_files.append(('reference_data.py', '.'))

a = Analysis(
    ['start.py'],
    pathex=[],
    binaries=[],
    datas=data_files,
    hiddenimports=[
        'openpyxl',
        'openpyxl.utils',
        'openpyxl.workbook',
        'openpyxl.worksheet',
        'docx',
        'docx.shared',
        'docx.enum.text',
        'reference_data',
        'employee_management_simple',
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.filedialog',
        'tkinter.font'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='نظام_إدارة_الموظفين',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # إخفاء نافذة الكونسول
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # يمكن إضافة أيقونة هنا لاحقاً
)
