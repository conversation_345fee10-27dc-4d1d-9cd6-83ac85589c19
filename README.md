# نظام إدارة الموظفين المبسط

## نظرة عامة
نظام إدارة الموظفين المبسط هو تطبيق مبسط ومتكامل لإدارة بيانات الموظفين. تم تبسيطه ليركز على الوظائف الأساسية فقط.

## الميزات الرئيسية

### 📊 إدارة البيانات
- إضافة وتعديل بيانات الموظفين
- البحث والتصفية البسيطة
- التحقق من صحة البيانات الأساسية
- حفظ البيانات في ملف Excel

### 📈 الإحصائيات المبسطة والمركزة
- إحصائيات أساسية ومهمة عن الموظفين
- المجموع العام لعدد الموظفين
- توزيع الجنس حسب الرقم الوطني (1=ذكر، 2=أنثى)
- عدد الموظفين حسب مكان العمل مع النسب المئوية
- واجهة مبسطة وسهلة القراءة

### 🖱️ وظائف الماوس الكاملة
- **النقر المفرد**: تحديد الموظف مع عرض المعلومات في شريط الحالة
- **النقر المزدوج**: فتح نموذج التعديل مباشرة
- **الزر الأيمن**: قائمة سياق شاملة (تعديل، نسخ، حذف)
- **عجلة الماوس**: تمرير سريع في الجدول

### 🖥️ أحجام شاشات محسنة
- **النافذة الرئيسية**: 1200x800 (متجاوبة مع حجم الشاشة)
- **نافذة الإضافة/التعديل**: 1000x700 مع 3 أعمدة للحقول
- **نافذة الإحصائيات**: 800x600 مع تمرير كامل
- **توسيط تلقائي**: جميع النوافذ تتوسط في الشاشة

### 🔧 واجهة بسيطة
- واجهة مستخدم مبسطة وسهلة الاستخدام
- دعم اللغة العربية
- اختصارات لوحة المفاتيح
- شريط حالة تفاعلي ومعلوماتي

## بنية المجلدات

```
قسم إدارة الموظفين/
├── data/                          # ملفات البيانات
│   └── employees_data.xlsx        # بيانات الموظفين الرئيسية
├── employee_management_simple.py  # نظام إدارة الموظفين المبسط
├── reference_data.py              # البيانات المرجعية
├── employee_config.json           # ملف الإعدادات
├── start.py                       # ملف التشغيل الرئيسي
├── view_preferences.json          # تفضيلات العرض
├── تشغيل_النظام.bat               # ملف التشغيل السريع
├── تثبيت_مكتبة_Word.bat           # ملف تثبيت مكتبة Word
└── README.md                      # هذا الملف
```

## المتطلبات

### البرامج المطلوبة
- Python 3.7 أو أحدث
- نظام التشغيل Windows 10 أو أحدث

### المكتبات المطلوبة
- `tkinter` (مدمجة مع Python)
- `openpyxl` (للتعامل مع ملفات Excel)
- `python-docx` (اختياري - لتصدير الإحصائيات إلى ملفات Word)

### تثبيت المتطلبات الأساسية
```bash
pip install openpyxl
```

### تثبيت مكتبة Word (اختياري)
```bash
pip install python-docx
```
أو استخدام ملف التثبيت المرفق (موصى به):
```
تثبيت_مكتبة_Word.bat
```

**ملاحظة**: إذا واجهت مشاكل في تصدير الإحصائيات إلى ملفات Word، قم بتشغيل ملف `تثبيت_مكتبة_Word.bat` الذي سيقوم بتثبيت المكتبة والتحقق من عملها بشكل صحيح.

## كيفية التشغيل

### الطريقة الأولى: التشغيل المباشر (الموصى بها)
```bash
python start.py
```

### الطريقة الثانية: ملف الدفعة (Windows)
```bash
تشغيل_النظام.bat
```

## الاستخدام

### إضافة موظف جديد
1. انقر على زر "➕ إضافة موظف"
2. املأ البيانات المطلوبة (الحقول المميزة بـ * مطلوبة)
3. انقر على "💾 حفظ"

### تعديل بيانات موظف
1. **تحديد الموظف**: انقر على صف الموظف في الجدول لتحديده
2. **فتح نموذج التعديل**:
   - انقر على زر "✏️ تعديل" في شريط الأدوات، أو
   - اضغط مرتين على صف الموظف، أو
   - انقر بالزر الأيمن واختر "تعديل البيانات"
3. **تعديل البيانات**: عدل البيانات المطلوبة في النموذج
4. **حفظ التغييرات**: انقر على "💾 تحديث"

### البحث والتصفية
- استخدم مربع البحث للبحث في جميع الحقول
- استخدم قوائم التصفية لتصفية حسب مكان العمل أو المؤهل أو الجنسية
- انقر على "🔄 إعادة تعيين" لمسح جميع التصفيات

### عرض الإحصائيات وتصديرها
- انقر على زر "📊 إحصائيات" لفتح نافذة إحصائيات مبسطة
- **المحتوى**:
  - **📊 المجموع العام**: إجمالي عدد الموظفين
  - **👥 توزيع الجنس**: الذكور والإناث حسب الرقم الوطني
  - **🏢 أماكن العمل**: توزيع الموظفين حسب مكان العمل
- **تصدير الإحصائيات**:
  - انقر على زر "📄 تصدير الإحصائيات" لتصدير البيانات
  - يمكنك اختيار مكان حفظ الملف واسمه
  - **خيارات التصدير**:
    - **ملف Word (DOCX)**: تقرير منسق مع جداول وعناوين (يتطلب تثبيت مكتبة python-docx)
    - **ملف نصي (TXT)**: تقرير بسيط بتنسيق نصي (متاح دائماً)
  - بعد التصدير، يمكنك فتح الملف مباشرة من النظام

## اختصارات لوحة المفاتيح

| المفتاح | الوظيفة |
|---------|---------|
| Ctrl+N | إضافة موظف جديد |
| Ctrl+E | تعديل الموظف المحدد |
| Ctrl+F | التركيز على البحث |
| Ctrl+R | تحديث البيانات |
| F5 | تحديث |
| Esc | مسح البحث |

## حل المشاكل

### مشكلة: النظام لا يبدأ
**الحل:**
1. تأكد من تثبيت Python 3.7+
2. تأكد من تثبيت openpyxl: `pip install openpyxl`
3. تشغيل الأمر: `python start.py`

### مشكلة: خطأ في قراءة ملف Excel
**الحل:**
1. تأكد من وجود مجلد `data`
2. تأكد من عدم فتح ملف `employees_data.xlsx` في برنامج آخر
3. تأكد من صلاحيات القراءة والكتابة

## الترخيص

هذا البرنامج مخصص للاستخدام الداخلي فقط.

## إصدارات التحديث

### الإصدار 3.2.2 (إصلاح مشاكل التصدير)
- تحسين معالجة الأخطاء في تصدير الإحصائيات
- إضافة تشخيص مفصل للمشاكل
- تحسين ملف تثبيت مكتبة Word
- إضافة خيارات بديلة للتصدير في حالة فشل الخيار الأساسي
- تحسين رسائل الخطأ للمستخدم

### الإصدار 3.2.1 (تبسيط خيارات التصدير)
- تبسيط واجهة تصدير الإحصائيات
- إصلاح مشكلة تصدير ملفات Word
- تحسين تنسيق ملفات التصدير
- إزالة خيارات التصدير غير المستقرة

### الإصدار 3.1 (إضافة تصدير الإحصائيات)
- إضافة ميزة تصدير الإحصائيات إلى ملفات Word
- إضافة دعم للملفات النصية كبديل لملفات Word
- إضافة ملف تثبيت مكتبة Word
- تحسين واجهة نافذة الإحصائيات

### الإصدار 3.0 (المبسط والمنظف)
- تنظيف الكود وإزالة الملفات الزائدة
- توحيد ملفات التشغيل
- تحسين الأداء والاستقرار
- تبسيط واجهة المستخدم

### الإصدار 2.1 (المبسط)
- إزالة وظائف التصدير والنسخ الاحتياطية
- تبسيط الواجهة والوظائف
- التركيز على الوظائف الأساسية فقط
- تحسين الأداء والاستقرار
