#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل نظام إدارة الموظفين المبسط
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

def main():
    """تشغيل نظام إدارة الموظفين المبسط"""
    try:
        print("🚀 بدء تشغيل نظام إدارة الموظفين المبسط...")

        # استيراد النظام
        from employee_management_simple import EmployeeManagementSystem

        # إنشاء النافذة الرئيسية
        root = tk.Tk()

        # إنشاء وتشغيل النظام
        app = EmployeeManagementSystem(root)

        # تشغيل التطبيق
        root.mainloop()

    except ImportError as e:
        error_msg = f"خطأ في استيراد الوحدات:\n{str(e)}\n\nتأكد من تثبيت openpyxl:\npip install openpyxl"
        print(f"❌ {error_msg}")
        try:
            messagebox.showerror("خطأ في التشغيل", error_msg)
        except:
            pass

    except Exception as e:
        error_msg = f"خطأ في تشغيل النظام:\n{str(e)}"
        print(f"❌ {error_msg}")
        try:
            messagebox.showerror("خطأ", error_msg)
        except:
            pass

if __name__ == "__main__":
    main()
