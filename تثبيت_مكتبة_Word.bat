@echo off
echo ========================================
echo    تثبيت مكتبة Word للإحصائيات
echo ========================================
echo.

REM التحقق من وجود Python
python --version > nul 2>&1
if %errorlevel% neq 0 (
    echo [!] خطأ: لم يتم العثور على Python. يرجى تثبيت Python أولاً.
    goto end
)

echo [+] تم العثور على Python بنجاح.
echo.
echo [*] جاري تثبيت مكتبة python-docx...
echo.

REM تثبيت المكتبة
pip install --upgrade pip
pip install python-docx --upgrade

echo.
if %errorlevel% neq 0 (
    echo [!] حدث خطأ أثناء تثبيت المكتبة.
) else (
    echo [+] تم تثبيت مكتبة python-docx بنجاح!
)

echo.
echo [*] التحقق من التثبيت...
python -c "from docx import Document; print('تم التحقق من المكتبة بنجاح!')" > nul 2>&1
if %errorlevel% neq 0 (
    echo [!] تحذير: تم تثبيت المكتبة ولكن قد تكون هناك مشكلة في استخدامها.
) else (
    echo [+] تم التحقق من المكتبة بنجاح!
)

:end
echo.
echo ========================================
echo اضغط أي مفتاح للخروج...
pause > nul