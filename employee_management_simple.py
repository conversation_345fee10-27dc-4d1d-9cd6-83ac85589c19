#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الموظفين المبسط
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, font
import os
import shutil
from datetime import datetime, timedelta
import json
import threading
import time
import glob

# استيراد البيانات المرجعية
try:
    from reference_data import get_bank_names, get_nationalities, get_qualifications, get_work_places
except ImportError:
    print("⚠️ تحذير: لا يمكن استيراد البيانات المرجعية")
    def get_bank_names(): return ["مصرف افتراضي"]
    def get_nationalities(): return ["ليبي", "ليبية"]
    def get_qualifications(): return ["بكالوريوس", "ليسانس"]
    def get_work_places(): return ["مكان عمل افتراضي"]

# استيراد مكتبة Excel
try:
    from openpyxl import Workbook, load_workbook
    from openpyxl.utils import get_column_letter
    EXCEL_AVAILABLE = True
except ImportError:
    EXCEL_AVAILABLE = False
    print("[خطأ] مكتبة openpyxl غير مثبتة")

# استيراد مكتبة Word
try:
    from docx import Document
    from docx.shared import Pt, RGBColor, Inches, Cm
    from docx.enum.text import WD_ALIGN_PARAGRAPH
    # التحقق من أن المكتبة تعمل بشكل صحيح
    test_doc = Document()
    test_doc.add_paragraph("Test")
    WORD_AVAILABLE = True
    print("[OK] تم التحقق من مكتبة python-docx بنجاح")
except Exception as e:
    WORD_AVAILABLE = False
    print(f"[تحذير] مشكلة في مكتبة python-docx: {str(e)}")
    print("[تحذير] سيتم استخدام ملفات نصية بدلاً من ملفات Word")

class EmployeeManagementSystem:
    """نظام إدارة الموظفين المبسط"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("نظام إدارة الموظفين المبسط")
        self.root.geometry("1200x800")
        self.root.configure(bg="#f0f0f0")
        
        # متغيرات النسخ الاحتياطي
        self.backup_thread = None
        self.backup_running = False
        
        # متغيرات واجهة المستخدم
        self.current_font_size = "medium"  # small, medium, large
        self.current_font_family = "Arial"
        self.dark_mode = False
        self.show_tooltips = True
        
        # تعريف أحجام الخطوط
        self.font_sizes = {
            "small": {
                "table": 9,
                "button": 9,
                "label": 9,
                "entry": 9,
                "title": 12
            },
            "medium": {
                "table": 10,
                "button": 10,
                "label": 10,
                "entry": 10,
                "title": 14
            },
            "large": {
                "table": 14,
                "button": 14,
                "label": 14,
                "entry": 14,
                "title": 18
            }
        }
        
        # تعريف ألوان الوضع العادي والوضع الداكن
        self.color_schemes = {
            "light": {
                "bg": "#f0f0f0",
                "fg": "#333333",
                "button_bg": "#3498db",
                "button_fg": "white",
                "table_bg": "white",
                "table_fg": "black",
                "highlight_bg": "#e0e0e0",
                "toolbar_bg": "#2c3e50"
            },
            "dark": {
                "bg": "#2c3e50",
                "fg": "#ecf0f1",
                "button_bg": "#34495e",
                "button_fg": "#ecf0f1",
                "table_bg": "#34495e",
                "table_fg": "#ecf0f1",
                "highlight_bg": "#3c5a76",
                "toolbar_bg": "#1a2530"
            }
        }

        # توسيط النافذة
        self.center_window(self.root, 1200, 800)

        # إعداد الملفات والمجلدات
        self.setup_directories()
        self.setup_files()
        
        # تهيئة البيانات الأساسية
        self.initialize_basic_data()
        
        # تهيئة متغيرات البحث
        self.initialize_search_vars()

        # إنشاء الواجهة
        self.create_interface()

        # تحميل البيانات
        self.load_employees_data()
        
        # تحميل إعدادات حجم الخط
        self.load_font_settings()
        
        # تطبيق حجم الخط
        self.apply_font_size()
        
        # بدء خدمة النسخ الاحتياطي إذا كانت مفعلة
        self.start_backup_service()
        
        print("[تم] تهيئة نظام إدارة الموظفين المبسط بنجاح")
    
    def setup_directories(self):
        """إنشاء المجلدات المطلوبة"""
        directories = ["data"]
        
        # إضافة مجلد النسخ الاحتياطي إذا كان مفعلاً
        if hasattr(self, 'config') and self.config.get("backup", {}).get("enabled", False):
            backup_dir = self.config.get("backup", {}).get("backup_dir", "backups")
            directories.append(backup_dir)
        
        for directory in directories:
            if not os.path.exists(directory):
                os.makedirs(directory)
                print(f"[تم] إنشاء مجلد: {directory}")
    
    def setup_files(self):
        """إعداد ملفات النظام"""
        # تحميل ملف الإعدادات
        self.config_file = "employee_config.json"
        self.load_config()
        
        # تعيين مسار ملف البيانات
        self.excel_file = self.config.get("data_paths", {}).get("employees_file", "data/employees_data.xlsx")
        self.sheet_name = "الموظفين"
        
        # إنشاء ملف Excel إذا لم يكن موجوداً
        if not os.path.exists(self.excel_file):
            self.create_empty_excel_file()
            
    def load_config(self):
        """تحميل ملف الإعدادات"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
                print(f"[تم] تم تحميل ملف الإعدادات: {self.config_file}")
            else:
                # إنشاء ملف إعدادات افتراضي
                self.config = {
                    "ui_settings": {
                        "theme": "light",
                        "font_size": self.current_font_size,
                        "rtl_support": True
                    },
                    "data_paths": {
                        "employees_file": "data/employees_data.xlsx"
                    },
                    "system_info": {
                        "version": "3.4.0",
                        "last_updated": datetime.now().strftime("%Y-%m-%d"),
                        "created_by": "نظام إدارة الموارد البشرية",
                        "description": "نظام إدارة الموظفين المبسط مع دعم النسخ الاحتياطي"
                    },
                    "backup": {
                        "enabled": True,
                        "schedule": "daily",
                        "time": "00:00",
                        "keep_days": 7,
                        "backup_dir": "backups",
                        "last_backup": None
                    }
                }
                
                # حفظ الإعدادات الافتراضية
                with open(self.config_file, 'w', encoding='utf-8') as f:
                    json.dump(self.config, f, ensure_ascii=False, indent=4)
                print(f"[تم] تم إنشاء ملف إعدادات افتراضي: {self.config_file}")
        except Exception as e:
            print(f"[خطأ] فشل في تحميل ملف الإعدادات: {e}")
            # إنشاء إعدادات افتراضية في الذاكرة
            self.config = {
                "data_paths": {"employees_file": "data/employees_data.xlsx"},
                "backup": {"enabled": False}
            }
    
    def initialize_basic_data(self):
        """تهيئة البيانات الأساسية"""
        # البيانات المرجعية
        self.banks = get_bank_names()
        self.nationalities = get_nationalities()
        self.qualifications = get_qualifications()
        self.work_places = get_work_places()
        self.grades = [str(i) for i in range(1, 15)]
        
        # بيانات الموظفين
        self.employees_data = []
        self.filtered_data = []
        self.selected_employee = None
        
        # تحميل تفضيلات العرض
        self.load_view_preferences()
        
        print("[تم] تهيئة البيانات الأساسية")
    
    def load_view_preferences(self):
        """تحميل تفضيلات العرض"""
        try:
            if os.path.exists("view_preferences.json"):
                with open("view_preferences.json", "r", encoding="utf-8") as f:
                    prefs = json.load(f)
                    self.visible_columns = prefs.get("visible_columns", self.get_all_columns())
            else:
                self.visible_columns = self.get_all_columns()
        except Exception as e:
            print(f"[خطأ] خطأ في تحميل تفضيلات العرض: {e}")
            self.visible_columns = self.get_all_columns()
    
    def get_all_columns(self):
        """الحصول على جميع الأعمدة المتاحة"""
        return [
            "الرقم الوظيفي", "الاسم العربي", "المسمى الوظيفي", "مكان العمل الحالي",
            "الرقم الوطني", "الرقم المالي", "المؤهل", "الدرجة الحالية",
            "العلاوة", "التخصص", "الجنسية", "تاريخ الميلاد", "تاريخ أول مباشرة",
            "تاريخ التعيين", "تاريخ الدرجة الحالية", "رقم الحساب", "اسم المصرف",
            "رقم الهاتف"
        ]
    
    def initialize_search_vars(self):
        """تهيئة متغيرات البحث"""
        self.search_var = tk.StringVar()

        # ربط أحداث البحث
        self.search_var.trace('w', self.on_search_change)
    
    def create_interface(self):
        """إنشاء واجهة المستخدم"""
        # إنشاء شريط الأدوات
        self.create_toolbar()
        
        # إنشاء منطقة البحث والتصفية
        self.create_search_filter_area()

        # إنشاء الجدول
        self.create_table()

        # إنشاء شريط الحالة
        self.create_status_bar()

        # إعداد اختصارات لوحة المفاتيح
        self.setup_keyboard_shortcuts()
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar_frame = tk.Frame(self.root, bg="#2c3e50", height=60)
        toolbar_frame.pack(fill=tk.X, padx=5, pady=5)
        toolbar_frame.pack_propagate(False)
        
        # أزرار العمليات الأساسية
        buttons_data = [
            ("إضافة موظف", self.add_employee, "#27ae60"),
            ("تعديل", self.edit_employee, "#3498db"),
            ("حذف", self.delete_employee, "#dc3545"),
            ("بحث", self.search_employee, "#6f42c1"),
            ("تحديث", self.refresh_data, "#34495e"),
            ("إحصائيات", self.show_quick_statistics, "#e83e8c"),
            ("نسخ احتياطي", self.manual_backup, "#f39c12"),
            ("إعدادات النسخ", self.show_backup_settings, "#9b59b6"),
            ("تكبير الخط +", self.increase_font_size, "#2980b9"),
            ("تصغير الخط -", self.decrease_font_size, "#3498db"),
            ("تبديل المظهر", self.toggle_dark_mode, "#16a085"),
            ("إغلاق", self.close_window, "#e74c3c")
        ]

        for text, command, color in buttons_data:
            try:
                btn = tk.Button(toolbar_frame, text=text, command=command,
                               bg=color, fg="white", font=("Arial", 10, "bold"),
                               padx=15, pady=8, cursor="hand2", relief="flat")
                btn.pack(side=tk.LEFT, padx=5, pady=10)
                
                # إضافة تلميحات للأزرار
                if hasattr(self, 'show_tooltips') and self.show_tooltips:
                    if "تكبير" in text:
                        self.add_tooltip(btn, "تكبير حجم الخط (الحد الأقصى: كبير)")
                    elif "تصغير" in text:
                        self.add_tooltip(btn, "تصغير حجم الخط (الحد الأدنى: صغير)")
                    elif "تبديل المظهر" in text:
                        self.add_tooltip(btn, "تبديل بين الوضع العادي والوضع الداكن")
                    elif "إعدادات النسخ" in text:
                        self.add_tooltip(btn, "إعدادات النسخ الاحتياطي والاستعادة")
                    elif "إحصائيات" in text:
                        self.add_tooltip(btn, "عرض إحصائيات وتقارير عن الموظفين")
                    elif "نسخ احتياطي" in text:
                        self.add_tooltip(btn, "إنشاء نسخة احتياطية يدوياً")

                print(f"[تم] تم إنشاء زر: {text}")

            except Exception as e:
                print(f"[خطأ] خطأ في إنشاء زر {text}: {e}")
    
    def add_tooltip(self, widget, text):
        """إضافة تلميح عند تمرير المؤشر فوق العنصر"""
        tooltip = None
        
        def enter(event):
            nonlocal tooltip
            x, y, _, _ = widget.bbox("insert") if hasattr(widget, "bbox") else (0, 0, 0, 0)
            x += widget.winfo_rootx() + 25
            y += widget.winfo_rooty() + 25
            
            # إنشاء نافذة التلميح
            tooltip = tk.Toplevel(widget)
            tooltip.wm_overrideredirect(True)
            tooltip.wm_geometry(f"+{x}+{y}")
            
            # تحديد لون الخلفية حسب الوضع
            bg_color = "#2c3e50" if hasattr(self, 'dark_mode') and self.dark_mode else "#f0f0f0"
            fg_color = "#ecf0f1" if hasattr(self, 'dark_mode') and self.dark_mode else "#333333"
            
            # إنشاء إطار التلميح
            frame = tk.Frame(tooltip, bg=bg_color, bd=1, relief="solid")
            frame.pack(fill="both", expand=True)
            
            # إضافة نص التلميح
            font_family = self.current_font_family if hasattr(self, 'current_font_family') else "Arial"
            label = tk.Label(frame, text=text, justify="right", bg=bg_color, fg=fg_color,
                          font=(font_family, 9), padx=10, pady=5)
            label.pack()
        
        def leave(event):
            nonlocal tooltip
            if tooltip:
                tooltip.destroy()
                tooltip = None
        
        # ربط الأحداث
        widget.bind("<Enter>", enter)
        widget.bind("<Leave>", leave)

    def center_window(self, window, width, height):
        """توسيط النافذة في الشاشة"""
        try:
            # الحصول على أبعاد الشاشة
            screen_width = window.winfo_screenwidth()
            screen_height = window.winfo_screenheight()

            # حساب الموقع المناسب
            x = (screen_width - width) // 2
            y = (screen_height - height) // 2

            # تطبيق الموقع
            window.geometry(f"{width}x{height}+{x}+{y}")
        except Exception as e:
            print(f"خطأ في توسيط النافذة: {e}")

    def create_search_filter_area(self):
        """إنشاء منطقة البحث المبسطة"""
        search_frame = tk.LabelFrame(self.root, text="البحث",
                                   font=("Arial", 12, "bold"), bg="#f8f9fa")
        search_frame.pack(fill=tk.X, padx=10, pady=5)

        # صف البحث الوحيد
        search_row = tk.Frame(search_frame, bg="#f8f9fa")
        search_row.pack(fill=tk.X, padx=10, pady=10)

        tk.Label(search_row, text="🔍 البحث:", font=("Arial", 12, "bold"),
                bg="#f8f9fa").pack(side=tk.LEFT, padx=5)

        search_entry = tk.Entry(search_row, textvariable=self.search_var,
                              font=("Arial", 12), width=40)
        search_entry.pack(side=tk.LEFT, padx=10)

        # زر مسح البحث
        tk.Button(search_row, text="🗑️ مسح", command=self.clear_search,
                 bg="#95a5a6", fg="white", font=("Arial", 10, "bold"),
                 padx=15, pady=8).pack(side=tk.LEFT, padx=10)

    def create_table(self):
        """إنشاء جدول الموظفين"""
        table_container = tk.Frame(self.root, bg="#f0f0f0")
        table_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # إطار الجدول
        table_frame = tk.LabelFrame(table_container, text="قائمة الموظفين",
                                  font=("Arial", 12, "bold"))
        table_frame.pack(fill=tk.BOTH, expand=True)

        # إنشاء Treeview مع الأعمدة المرتبة
        columns = (
            "الرقم الوظيفي", "الاسم العربي", "المسمى الوظيفي", "مكان العمل الحالي",
            "الرقم الوطني", "المؤهل", "الجنسية", "رقم الهاتف"
        )

        self.employees_table = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)

        # تعيين عناوين الأعمدة
        for col in columns:
            self.employees_table.heading(col, text=col, anchor=tk.CENTER)
            self.employees_table.column(col, width=120, anchor=tk.CENTER)

        # إضافة شريط التمرير
        scrollbar_y = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.employees_table.yview)
        scrollbar_x = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.employees_table.xview)

        self.employees_table.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)

        # ترتيب العناصر
        self.employees_table.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)
        scrollbar_x.pack(side=tk.BOTTOM, fill=tk.X)

        # ربط أحداث الماوس ولوحة المفاتيح
        self.employees_table.bind("<Double-1>", self.on_double_click)
        self.employees_table.bind("<Button-3>", self.show_context_menu)
        self.employees_table.bind("<Return>", lambda e: self.edit_employee())

    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_frame = tk.Frame(self.root, bg="#34495e", height=25)
        self.status_frame.pack(fill=tk.X, side=tk.BOTTOM)
        self.status_frame.pack_propagate(False)

        self.status_label = tk.Label(self.status_frame, text="جاهز", 
                                   bg="#34495e", fg="white", font=("Arial", 9))
        self.status_label.pack(side=tk.LEFT, padx=10, pady=3)

    def setup_keyboard_shortcuts(self):
        """إعداد اختصارات لوحة المفاتيح"""
        self.root.bind("<Control-n>", lambda e: self.add_employee())
        self.root.bind("<Control-e>", lambda e: self.edit_employee())
        self.root.bind("<Delete>", lambda e: self.delete_employee())
        self.root.bind("<Control-f>", lambda e: self.search_employee())
        self.root.bind("<F5>", lambda e: self.refresh_data())
        self.root.bind("<Control-r>", lambda e: self.refresh_data())

    def create_empty_excel_file(self):
        """إنشاء ملف Excel فارغ"""
        if not EXCEL_AVAILABLE:
            print("[خطأ] لا يمكن إنشاء ملف Excel - مكتبة openpyxl غير متاحة")
            return

        try:
            wb = Workbook()
            ws = wb.active
            ws.title = self.sheet_name

            # إضافة العناوين
            headers = [
                "الرقم الوظيفي", "الاسم العربي", "المسمى الوظيفي", "مكان العمل الحالي",
                "الرقم الوطني", "الرقم المالي", "المؤهل", "الدرجة الحالية",
                "العلاوة", "التخصص", "الجنسية", "تاريخ الميلاد", "تاريخ أول مباشرة",
                "تاريخ التعيين", "تاريخ الدرجة الحالية", "رقم الحساب", "اسم المصرف",
                "رقم الهاتف"
            ]

            ws.append(headers)
            wb.save(self.excel_file)
            print(f"[تم] تم إنشاء ملف Excel جديد: {self.excel_file}")

        except Exception as e:
            print(f"[خطأ] خطأ في إنشاء ملف Excel: {e}")

    def update_status(self, message, info=""):
        """تحديث شريط الحالة"""
        try:
            if hasattr(self, 'status_label'):
                full_message = f"{message}"
                if info:
                    full_message += f" | {info}"
                self.status_label.config(text=full_message)
                self.root.update_idletasks()
        except Exception as e:
            print(f"خطأ في تحديث شريط الحالة: {e}")

    def load_employees_data(self):
        """تحميل بيانات الموظفين من Excel"""
        if not EXCEL_AVAILABLE:
            print("[خطأ] مكتبة openpyxl غير متاحة")
            return False

        try:
            # حفظ عدد الموظفين قبل التحميل للمقارنة
            previous_count = len(self.employees_data) if hasattr(self, 'employees_data') else 0
            
            if not os.path.exists(self.excel_file):
                print("[معلومات] ملف البيانات غير موجود، سيتم إنشاؤه عند الحفظ")
                self.employees_data = []
                self.update_employees_table()
                return True

            # التحقق من أن الملف ليس فارغاً
            if os.path.getsize(self.excel_file) == 0:
                print("[تحذير] ملف البيانات فارغ")
                self.employees_data = []
                self.update_employees_table()
                return True

            # محاولة فتح الملف
            try:
                wb = load_workbook(self.excel_file)
            except Exception as e:
                print(f"[خطأ] فشل في فتح ملف Excel: {e}")
                
                # التحقق من وجود نسخة احتياطية
                backup_file = f"{self.excel_file}.bak"
                if os.path.exists(backup_file):
                    try:
                        print("[محاولة] استعادة من النسخة الاحتياطية...")
                        wb = load_workbook(backup_file)
                        print("[تم] تم استعادة البيانات من النسخة الاحتياطية")
                    except Exception as backup_error:
                        print(f"[خطأ] فشل في استعادة النسخة الاحتياطية: {backup_error}")
                        self.employees_data = []
                        self.update_employees_table()
                        return False
                else:
                    self.employees_data = []
                    self.update_employees_table()
                    return False

            # التحقق من وجود الورقة المطلوبة
            if self.sheet_name in wb.sheetnames:
                ws = wb[self.sheet_name]
            else:
                print(f"[تحذير] الورقة '{self.sheet_name}' غير موجودة، سيتم استخدام الورقة النشطة")
                ws = wb.active

            self.employees_data = []
            
            # تحديد العناوين
            headers = [
                "الرقم الوظيفي", "الاسم العربي", "المسمى الوظيفي", "مكان العمل الحالي",
                "الرقم الوطني", "الرقم المالي", "المؤهل", "الدرجة الحالية",
                "العلاوة", "التخصص", "الجنسية", "تاريخ الميلاد", "تاريخ أول مباشرة",
                "تاريخ التعيين", "تاريخ الدرجة الحالية", "رقم الحساب", "اسم المصرف",
                "رقم الهاتف"
            ]

            # قراءة البيانات (تخطي الصف الأول - العناوين)
            row_count = 0
            for row in ws.iter_rows(min_row=2, values_only=True):
                row_count += 1
                if row and row[0]:  # إذا كان الصف غير فارغ والرقم الوظيفي موجود
                    emp_data = {}
                 
                    for i, header in enumerate(headers):
                        if i < len(row):
                            # تحويل القيم إلى نص
                            value = row[i]
                            if value is None:
                                value = ""
                            elif not isinstance(value, str):
                                value = str(value)
                            emp_data[header] = value
                        else:
                            emp_data[header] = ""
                    
                    # التحقق من صحة الرقم الوظيفي
                    try:
                        int(emp_data["الرقم الوظيفي"])
                        self.employees_data.append(emp_data)
                    except (ValueError, TypeError):
                        print(f"[تحذير] تم تخطي صف {row_count+1} بسبب رقم وظيفي غير صالح: {emp_data.get('الرقم الوظيفي', 'غير محدد')}")

            # ترتيب البيانات حسب الرقم الوظيفي
            try:
                self.employees_data = sorted(self.employees_data, key=lambda x: int(x.get("الرقم الوظيفي", "0") or "0"))
            except Exception as sort_error:
                print(f"[تحذير] فشل في ترتيب البيانات: {sort_error}")

            # عرض معلومات التحميل
            loaded_count = len(self.employees_data)
            print(f"[تم] تم تحميل {loaded_count} موظف من ملف Excel")
            
            if previous_count > 0 and loaded_count != previous_count:
                print(f"[معلومات] تغير عدد الموظفين من {previous_count} إلى {loaded_count}")
            
            self.update_employees_table()
            return True

        except Exception as e:
            print(f"[خطأ] خطأ في تحميل بيانات الموظفين: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحميل البيانات:\n{str(e)}")

    def update_employees_table(self):
        """تحديث جدول الموظفين مع مراعاة الأعمدة المرئية"""
        try:
            # التحقق من وجود الجدول
            if not hasattr(self, 'employees_table'):
                print("[خطأ] جدول الموظفين غير موجود")
                return

            print(f"[جاري] تحديث جدول الموظفين - عدد الموظفين: {len(self.employees_data)}")

            # حفظ العناصر المحددة حالياً
            selected_items = self.employees_table.selection()
            selected_values = []
            if selected_items:
                selected_values = self.employees_table.item(selected_items[0], "values")

            # إعادة تكوين الأعمدة حسب الأعمدة المرئية
            self.configure_table_columns()

            # مسح البيانات الحالية
            for item in self.employees_table.get_children():
                self.employees_table.delete(item)

            # إضافة البيانات المفلترة أو جميع البيانات
            data_to_show = self.filtered_data if self.filtered_data else self.employees_data
            print(f"[معلومات] عدد البيانات للعرض: {len(data_to_show)}")

            for i, emp in enumerate(data_to_show):
                values = []
                for col in self.employees_table["columns"]:
                    try:
                        value = emp.get(col, "")
                    except:
                        value = ""
                    values.append(str(value))

                # إضافة الصف للجدول
                self.employees_table.insert("", "end", values=values)

            # إعادة تحديد العنصر السابق إذا كان موجوداً
            if selected_values and len(selected_values) > 0:
                for item in self.employees_table.get_children():
                    values = self.employees_table.item(item, "values")
                    if values and values[0] == selected_values[0]:  # مقارنة الرقم الوظيفي
                        self.employees_table.selection_set(item)
                        self.employees_table.see(item)
                        break

            print(f"[تم] تم تحديث الجدول - تم إضافة {len(data_to_show)} صف")

            # تحديث شريط الحالة
            total_count = len(self.employees_data)
            shown_count = len(data_to_show)

            if shown_count != total_count:
                self.update_status(f"عرض {shown_count} من {total_count} موظف")
            else:
                self.update_status(f"إجمالي الموظفين: {total_count}")

        except Exception as e:
            print(f"خطأ في تحديث الجدول: {e}")
            
    def configure_table_columns(self):
        """إعادة تكوين أعمدة الجدول حسب الأعمدة المرئية"""
        try:
            # التحقق من وجود الجدول
            if not hasattr(self, 'employees_table'):
                return
                
            # التأكد من وجود أعمدة مرئية
            if not hasattr(self, 'visible_columns') or not self.visible_columns:
                self.visible_columns = self.get_all_columns()
                
            # إعادة تكوين الأعمدة
            self.employees_table["columns"] = self.visible_columns
            
            # إعادة تعيين عناوين الأعمدة
            for col in self.visible_columns:
                self.employees_table.heading(col, text=col)
                
                # تعيين عرض العمود حسب محتواه
                if col == "الاسم العربي":
                    width = 200
                elif col in ["المسمى الوظيفي", "مكان العمل الحالي"]:
                    width = 150
                elif col in ["تاريخ الميلاد", "تاريخ أول مباشرة", "تاريخ التعيين", "تاريخ الدرجة الحالية"]:
                    width = 120
                else:
                    width = 100
                    
                self.employees_table.column(col, width=width, minwidth=50)
                
        except Exception as e:
            print(f"[خطأ] خطأ في تكوين أعمدة الجدول: {e}")

    def on_search_change(self, *args):
        """معالجة تغيير نص البحث"""
        self.apply_filters()

    def apply_filters(self):
        """تطبيق البحث المبسط"""
        try:
            search_text = self.search_var.get().strip().lower()

            # إعادة تعيين البيانات المفلترة
            self.filtered_data = []

            # تطبيق البحث
            for emp in self.employees_data:
                # البحث في جميع الحقول
                if search_text:
                    found = False
                    for key, value in emp.items():
                        if value and str(value).lower().find(search_text) != -1:
                            found = True
                            break

                    if not found:
                        continue
                else:
                    # إذا لم يكن هناك نص بحث، أضف جميع الموظفين
                    pass

                # إضافة الموظف للبيانات المفلترة
                self.filtered_data.append(emp)

            # إذا لم يكن هناك بحث، عرض جميع البيانات
            if not search_text:
                self.filtered_data = self.employees_data.copy()

            # تحديث الجدول
            self.update_employees_table()

            # تحديث شريط الحالة
            total_count = len(self.employees_data)
            filtered_count = len(self.filtered_data)
            if filtered_count == total_count:
                self.update_status(f"عرض جميع الموظفين ({total_count})")
            else:
                self.update_status(f"عرض {filtered_count} من أصل {total_count} موظف")

        except Exception as e:
            print(f"[خطأ] خطأ في تطبيق البحث: {e}")
            self.filtered_data = self.employees_data.copy()
            self.update_employees_table()

    def clear_search(self):
        """مسح البحث"""
        self.search_var.set("")

    def on_double_click(self, event):
        """معالجة النقر المزدوج على صف في الجدول"""
        self.edit_employee()

    def show_context_menu(self, event):
        """عرض قائمة السياق عند النقر بالزر الأيمن"""
        try:
            # تحديد العنصر تحت المؤشر
            item = self.employees_table.identify_row(event.y)
            if not item:
                return
                
            # تحديد العنصر
            self.employees_table.selection_set(item)
            
            # إنشاء القائمة
            context_menu = tk.Menu(self.root, tearoff=0)
            context_menu.add_command(label="✏️ تعديل البيانات", command=self.edit_employee)
            context_menu.add_command(label="🗑️ حذف", command=self.delete_employee)
            context_menu.add_separator()
            
            # الحصول على بيانات الموظف المحدد
            values = self.employees_table.item(item, "values")
            if values:
                emp_id = values[0]
                emp_name = values[1]
                
                # إضافة خيارات النسخ
                context_menu.add_command(label=f"📋 نسخ الرقم الوظيفي: {emp_id}", 
                                       command=lambda: self.copy_to_clipboard(emp_id))
                context_menu.add_command(label=f"📋 نسخ الاسم: {emp_name}", 
                                       command=lambda: self.copy_to_clipboard(emp_name))
            
            # عرض القائمة
            context_menu.tk_popup(event.x_root, event.y_root)
            
        except Exception as e:
            print(f"[خطأ] خطأ في عرض قائمة السياق: {e}")

    def copy_to_clipboard(self, text):
        """نسخ النص إلى الحافظة"""
        self.root.clipboard_clear()
        self.root.clipboard_append(text)
        self.update_status(f"تم نسخ: {text}")

    def add_employee(self):
        """إضافة موظف جديد"""
        self.open_employee_form()

    def edit_employee(self):
        """تعديل بيانات الموظف المحدد"""
        selected_items = self.employees_table.selection()
        if not selected_items:
            messagebox.showinfo("تنبيه", "الرجاء تحديد موظف من القائمة أولاً")
            return
            
        # الحصول على بيانات الموظف المحدد
        values = self.employees_table.item(selected_items[0], "values")
        emp_id = values[0]
        
        # البحث عن الموظف في البيانات
        for emp in self.employees_data:
            if str(emp.get("الرقم الوظيفي")) == str(emp_id):
                self.selected_employee = emp
                self.open_employee_form(is_edit=True)
                return
                
        messagebox.showerror("خطأ", "لم يتم العثور على بيانات الموظف")

    def open_employee_form(self, is_edit=False):
        """فتح نموذج إضافة/تعديل موظف"""
        # إنشاء نافذة جديدة
        form_window = tk.Toplevel(self.root)
        form_window.title("تعديل بيانات موظف" if is_edit else "إضافة موظف جديد")
        form_window.geometry("1000x700")
        form_window.configure(bg="#f0f0f0")

        # توسيط النافذة
        self.center_window(form_window, 1000, 700)

        # جعل النافذة مودال (تمنع التفاعل مع النافذة الرئيسية)
        form_window.grab_set()
        form_window.transient(self.root)

        # إنشاء متغيرات النموذج
        form_vars = {}

        # تحديد الحقول المطلوبة
        required_fields = ["الرقم الوظيفي", "الاسم العربي", "المسمى الوظيفي", "مكان العمل الحالي"]

        # إنشاء إطار النموذج
        form_frame = tk.Frame(form_window, bg="#f0f0f0", padx=20, pady=20)
        form_frame.pack(fill=tk.BOTH, expand=True)

        # إنشاء عنوان النموذج
        title_label = tk.Label(form_frame, text="تعديل بيانات موظف" if is_edit else "إضافة موظف جديد",
                             font=("Arial", 16, "bold"), bg="#f0f0f0")
        title_label.pack(pady=10)

        # إنشاء إطار الحقول
        fields_frame = tk.Frame(form_frame, bg="#f0f0f0")
        fields_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        # تحديد الحقول المراد عرضها
        fields = [
            "الرقم الوظيفي", "الاسم العربي", "المسمى الوظيفي", "مكان العمل الحالي",
            "الرقم الوطني", "الرقم المالي", "المؤهل", "الدرجة الحالية",
            "العلاوة", "التخصص", "الجنسية", "تاريخ الميلاد", "تاريخ أول مباشرة",
            "تاريخ التعيين", "تاريخ الدرجة الحالية", "رقم الحساب", "اسم المصرف",
            "رقم الهاتف"
        ]

        # تقسيم الحقول إلى 3 أعمدة
        columns = 3
        rows = (len(fields) + columns - 1) // columns

        # إنشاء الحقول
        for i, field in enumerate(fields):
            # حساب الصف والعمود
            row = i % rows
            col = i // rows

            # إنشاء إطار الحقل
            field_frame = tk.Frame(fields_frame, bg="#f0f0f0")
            field_frame.grid(row=row, column=col, padx=10, pady=5, sticky="w")

            # إضافة علامة * للحقول المطلوبة
            label_text = field
            if field in required_fields:
                label_text += " *"

            # إنشاء عنوان الحقل
            label = tk.Label(field_frame, text=label_text, font=("Arial", 10),
                           bg="#f0f0f0", width=15, anchor="w")
            label.pack(side=tk.LEFT, padx=5)

            # إنشاء متغير للحقل
            var = tk.StringVar()
            form_vars[field] = var

            # تعيين القيمة الحالية إذا كان تعديل
            if is_edit and self.selected_employee:
                value = self.selected_employee.get(field, "")
                if value is not None:
                    var.set(str(value))

            # إنشاء حقل الإدخال المناسب مع أحجام مخصصة
            if field == "الاسم العربي":
                # حقل أكبر للاسم العربي
                widget = tk.Entry(field_frame, textvariable=var, width=35, font=("Arial", 11))
                widget.pack(side=tk.LEFT, padx=5)
            elif field == "اسم المصرف":
                # حقل أكبر لاسم المصرف
                widget = ttk.Combobox(field_frame, textvariable=var, values=self.banks, width=35, font=("Arial", 10))
                widget.pack(side=tk.LEFT, padx=5)
            elif field == "المؤهل":
                widget = ttk.Combobox(field_frame, textvariable=var, values=self.qualifications, width=25)
                widget.pack(side=tk.LEFT, padx=5)
            elif field == "الجنسية":
                widget = ttk.Combobox(field_frame, textvariable=var, values=self.nationalities, width=20)
                widget.pack(side=tk.LEFT, padx=5)
            elif field == "مكان العمل الحالي":
                widget = ttk.Combobox(field_frame, textvariable=var, values=self.work_places, width=25)
                widget.pack(side=tk.LEFT, padx=5)
            elif field == "المسمى الوظيفي":
                # حقل أكبر للمسمى الوظيفي
                widget = tk.Entry(field_frame, textvariable=var, width=30)
                widget.pack(side=tk.LEFT, padx=5)
            elif field == "الدرجة الحالية":
                widget = ttk.Combobox(field_frame, textvariable=var, values=self.grades, width=20)
                widget.pack(side=tk.LEFT, padx=5)
            elif field in ["تاريخ الميلاد", "تاريخ أول مباشرة", "تاريخ التعيين", "تاريخ الدرجة الحالية"]:
                widget = tk.Entry(field_frame, textvariable=var, width=20)
                widget.pack(side=tk.LEFT, padx=5)
                # إضافة تلميح
                tk.Label(field_frame, text="(YYYY-MM-DD)", font=("Arial", 8),
                       bg="#f0f0f0", fg="#666").pack(side=tk.LEFT)
            else:
                widget = tk.Entry(field_frame, textvariable=var, width=20)
                widget.pack(side=tk.LEFT, padx=5)

        
        # إنشاء إطار الأزرار
        buttons_frame = tk.Frame(form_frame, bg="#f0f0f0")
        buttons_frame.pack(pady=20)

        # زر آخر رقم وظيفي (فقط في حالة الإضافة)
        if not is_edit:
            last_id_button = tk.Button(buttons_frame, text="🔢 آخر رقم وظيفي",
                                     command=lambda: self.get_last_employee_id(form_vars),
                                     bg="#3498db", fg="white", font=("Arial", 12, "bold"),
                                     padx=20, pady=10)
            last_id_button.pack(side=tk.LEFT, padx=10)

        # زر الحفظ
        save_button = tk.Button(buttons_frame, text="💾 حفظ",
                              command=lambda: self.save_employee_data(form_vars, form_window, is_edit),
                              bg="#27ae60", fg="white", font=("Arial", 12, "bold"),
                              padx=20, pady=10)
        save_button.pack(side=tk.LEFT, padx=10)

        # زر الإلغاء
        cancel_button = tk.Button(buttons_frame, text="❌ إلغاء",
                                command=form_window.destroy,
                                bg="#e74c3c", fg="white", font=("Arial", 12, "bold"),
                                padx=20, pady=10)
        cancel_button.pack(side=tk.LEFT, padx=10)

    def save_employee_data(self, form_vars, form_window, is_edit):
        """حفظ بيانات الموظف"""
        try:
            # التحقق من الحقول المطلوبة
            required_fields = ["الرقم الوظيفي", "الاسم العربي", "المسمى الوظيفي", "مكان العمل الحالي"]
            for field in required_fields:
                if not form_vars[field].get().strip():
                    messagebox.showerror("خطأ", f"الرجاء إدخال {field}")
                    return
            
            # التحقق من صحة الرقم الوظيفي (يجب أن يكون رقماً)
            emp_id = form_vars["الرقم الوظيفي"].get().strip()
            try:
                int(emp_id)  # محاولة تحويل الرقم الوظيفي إلى عدد صحيح
            except ValueError:
                messagebox.showerror("خطأ", "الرقم الوظيفي يجب أن يكون رقماً")
                return
            
            # جمع البيانات من النموذج
            employee_data = {}
            for field, var in form_vars.items():
                employee_data[field] = var.get().strip()
            
            # التحقق من عدم تكرار الرقم الوظيفي
            if not is_edit:
                for emp in self.employees_data:
                    if str(emp.get("الرقم الوظيفي")) == str(emp_id):
                        messagebox.showerror("خطأ", f"الرقم الوظيفي {emp_id} موجود مسبقاً")
                        return
            elif is_edit and str(self.selected_employee.get("الرقم الوظيفي")) != str(emp_id):
                # إذا تم تغيير الرقم الوظيفي أثناء التعديل، نتحقق من عدم تكراره
                for emp in self.employees_data:
                    if str(emp.get("الرقم الوظيفي")) == str(emp_id) and emp != self.selected_employee:
                        messagebox.showerror("خطأ", f"الرقم الوظيفي {emp_id} موجود مسبقاً")
                        return
            
            # تحديث البيانات أو إضافة موظف جديد
            if is_edit:
                # تحديث البيانات
                found = False
                for i, emp in enumerate(self.employees_data):
                    if str(emp.get("الرقم الوظيفي")) == str(self.selected_employee.get("الرقم الوظيفي")):
                        self.employees_data[i] = employee_data
                        found = True
                        break
                
                if not found:
                    messagebox.showerror("خطأ", "لم يتم العثور على الموظف المحدد")
                    return
                
                message = f"تم تحديث بيانات الموظف: {employee_data['الاسم العربي']}"
                print(f"[تم] تحديث بيانات الموظف: {employee_data['الاسم العربي']} (الرقم الوظيفي: {emp_id})")
            else:
                # إضافة موظف جديد
                self.employees_data.append(employee_data)
                message = f"تم إضافة الموظف: {employee_data['الاسم العربي']}"
                print(f"[تم] إضافة موظف جديد: {employee_data['الاسم العربي']} (الرقم الوظيفي: {emp_id})")
            
            # حفظ البيانات في ملف Excel
            save_result = self.save_to_excel()
            if not save_result:
                messagebox.showerror("خطأ", "فشل في حفظ البيانات في ملف Excel")
                return
            
            # تحديث الجدول
            self.update_employees_table()
            
            # إغلاق النافذة
            form_window.destroy()
            
            # عرض رسالة نجاح
            messagebox.showinfo("تم بنجاح", message)
            
        except Exception as e:
            error_message = f"حدث خطأ أثناء حفظ البيانات:\n{str(e)}"
            messagebox.showerror("خطأ", error_message)
            print(f"[خطأ] خطأ في حفظ بيانات الموظف: {e}")

    def save_to_excel(self):
        """حفظ البيانات في ملف Excel"""
        if not EXCEL_AVAILABLE:
            messagebox.showerror("خطأ", "مكتبة openpyxl غير متاحة")
            return False
            
        try:
            # إنشاء مجلد البيانات إذا لم يكن موجوداً
            if not os.path.exists("data"):
                os.makedirs("data")
            
            # إنشاء نسخة احتياطية من الملف الحالي إذا كان موجوداً
            if os.path.exists(self.excel_file):
                backup_file = f"{self.excel_file}.bak"
                try:
                    shutil.copy2(self.excel_file, backup_file)
                    print(f"[تم] إنشاء نسخة احتياطية: {backup_file}")
                except Exception as backup_error:
                    print(f"[تحذير] فشل في إنشاء نسخة احتياطية: {backup_error}")
            
            # إنشاء ملف Excel جديد
            wb = Workbook()
            ws = wb.active
            ws.title = self.sheet_name
            
            # إضافة العناوين
            headers = [
                "الرقم الوظيفي", "الاسم العربي", "المسمى الوظيفي", "مكان العمل الحالي",
                "الرقم الوطني", "الرقم المالي", "المؤهل", "الدرجة الحالية",
                "العلاوة", "التخصص", "الجنسية", "تاريخ الميلاد", "تاريخ أول مباشرة",
                "تاريخ التعيين", "تاريخ الدرجة الحالية", "رقم الحساب", "اسم المصرف",
                "رقم الهاتف"
            ]
            ws.append(headers)
            
            # ترتيب البيانات حسب الرقم الوظيفي
            sorted_employees = sorted(self.employees_data, key=lambda x: int(x.get("الرقم الوظيفي", "0") or "0"))
            
            # إضافة البيانات
            for emp in sorted_employees:
                row_data = []
                for header in headers:
                    row_data.append(emp.get(header, ""))
                ws.append(row_data)
            
            # تنسيق الجدول
            for col in range(1, len(headers) + 1):
                col_letter = get_column_letter(col)
                ws.column_dimensions[col_letter].width = 15  # تعيين عرض العمود
            
            # حفظ الملف
            wb.save(self.excel_file)
            print(f"[تم] تم حفظ البيانات في ملف Excel: {self.excel_file}")
            return True
            
        except Exception as e:
            error_message = f"حدث خطأ أثناء حفظ البيانات في Excel:\n{str(e)}"
            messagebox.showerror("خطأ", error_message)
            print(f"[خطأ] خطأ في حفظ البيانات في Excel: {e}")
            
            # محاولة استعادة النسخة الاحتياطية
            backup_file = f"{self.excel_file}.bak"
            if os.path.exists(backup_file):
                try:
                    shutil.copy2(backup_file, self.excel_file)
                    print(f"[تم] استعادة النسخة الاحتياطية")
                except Exception as restore_error:
                    print(f"[خطأ] فشل في استعادة النسخة الاحتياطية: {restore_error}")
            
            return False

    def delete_employee(self):
        """حذف الموظف المحدد"""
        try:
            print("[تشخيص] بدء عملية حذف الموظف")

            selected_items = self.employees_table.selection()
            if not selected_items:
                messagebox.showinfo("تنبيه", "الرجاء تحديد موظف من القائمة أولاً")
                return

            print(f"[تشخيص] تم تحديد عنصر: {selected_items[0]}")

            # الحصول على بيانات الموظف المحدد
            values = self.employees_table.item(selected_items[0], "values")
            if not values or len(values) < 2:
                messagebox.showerror("خطأ", "لا يمكن الحصول على بيانات الموظف المحدد")
                return

            emp_id = values[0]
            emp_name = values[1] if len(values) > 1 else "غير محدد"

            print(f"[تشخيص] الموظف المحدد: {emp_name} (الرقم الوظيفي: {emp_id})")

            # تأكيد الحذف
            confirm = messagebox.askyesno("تأكيد الحذف",
                                        f"هل أنت متأكد من حذف الموظف:\n{emp_name} (الرقم الوظيفي: {emp_id})؟")
            if not confirm:
                print("[تشخيص] تم إلغاء عملية الحذف من قبل المستخدم")
                return

            print(f"[تشخيص] عدد الموظفين قبل الحذف: {len(self.employees_data)}")

            # حذف الموظف من البيانات
            deleted = False
            for i, emp in enumerate(self.employees_data):
                if str(emp.get("الرقم الوظيفي")) == str(emp_id):
                    print(f"[تشخيص] تم العثور على الموظف في الفهرس: {i}")
                    del self.employees_data[i]
                    deleted = True
                    break

            if not deleted:
                messagebox.showerror("خطأ", f"لم يتم العثور على الموظف برقم وظيفي: {emp_id}")
                return

            print(f"[تشخيص] عدد الموظفين بعد الحذف: {len(self.employees_data)}")

            # حفظ البيانات في ملف Excel
            print("[تشخيص] جاري حفظ البيانات في Excel...")
            save_result = self.save_to_excel()
            if not save_result:
                messagebox.showerror("خطأ", "فشل في حفظ البيانات بعد الحذف")
                return

            print("[تشخيص] تم حفظ البيانات بنجاح")

            # تحديث البيانات المفلترة أيضاً
            if hasattr(self, 'filtered_data'):
                self.filtered_data = [emp for emp in self.filtered_data
                                    if str(emp.get("الرقم الوظيفي")) != str(emp_id)]
                print(f"[تشخيص] تم تحديث البيانات المفلترة: {len(self.filtered_data)}")

            # تحديث الجدول
            print("[تشخيص] جاري تحديث الجدول...")
            self.update_employees_table()

            # تحديث شريط الحالة
            self.update_status(f"تم حذف الموظف: {emp_name}")

            # عرض رسالة نجاح
            messagebox.showinfo("تم بنجاح", f"تم حذف الموظف: {emp_name}")
            print(f"[تم] تم حذف الموظف بنجاح: {emp_name} (الرقم الوظيفي: {emp_id})")

        except Exception as e:
            error_message = f"حدث خطأ أثناء حذف الموظف:\n{str(e)}"
            messagebox.showerror("خطأ", error_message)
            print(f"[خطأ] خطأ في حذف الموظف: {e}")
            import traceback
            traceback.print_exc()

    def search_employee(self):
        """التركيز على حقل البحث"""
        for widget in self.root.winfo_children():
            if isinstance(widget, tk.LabelFrame) and widget.cget("text") == "البحث":
                for child in widget.winfo_children():
                    if isinstance(child, tk.Frame):
                        for w in child.winfo_children():
                            if isinstance(w, tk.Entry):
                                w.focus_set()
                                return

    def refresh_data(self):
        """تحديث البيانات من ملف Excel"""
        self.update_status("جاري تحديث البيانات...")
        
        # حفظ عدد الموظفين قبل التحديث
        previous_count = len(self.employees_data)
        
        # تحميل البيانات
        result = self.load_employees_data()
        
        if result:
            # حساب الفرق في عدد الموظفين
            current_count = len(self.employees_data)
            diff = current_count - previous_count
            
            if diff > 0:
                message = f"تم تحديث البيانات بنجاح. تمت إضافة {diff} موظف جديد."
            elif diff < 0:
                message = f"تم تحديث البيانات بنجاح. تم حذف {abs(diff)} موظف."
            else:
                message = "تم تحديث البيانات بنجاح. لم يتغير عدد الموظفين."
            
            self.update_status(f"تم التحديث - {current_count} موظف")
            messagebox.showinfo("تم بنجاح", message)
        else:
            self.update_status("فشل في تحديث البيانات")
            messagebox.showerror("خطأ", "فشل في تحديث البيانات من ملف Excel")

    def show_quick_statistics(self):
        """عرض إحصائيات سريعة"""
        # إنشاء نافذة جديدة
        stats_window = tk.Toplevel(self.root)
        stats_window.title("إحصائيات الموظفين")
        stats_window.geometry("800x600")
        stats_window.configure(bg="#f0f0f0")
        
        # توسيط النافذة
        self.center_window(stats_window, 800, 600)
        
        # جعل النافذة مودال (تمنع التفاعل مع النافذة الرئيسية)
        stats_window.grab_set()
        stats_window.transient(self.root)
        
        # إنشاء إطار الإحصائيات
        stats_frame = tk.Frame(stats_window, bg="#f0f0f0", padx=20, pady=20)
        stats_frame.pack(fill=tk.BOTH, expand=True)
        
        # إنشاء عنوان الإحصائيات
        title_label = tk.Label(stats_frame, text="إحصائيات الموظفين",
                             font=("Arial", 16, "bold"), bg="#f0f0f0")
        title_label.pack(pady=10)
        
        # إجمالي عدد الموظفين
        total_frame = tk.LabelFrame(stats_frame, text="إجمالي الموظفين", 
                                  font=("Arial", 12, "bold"), bg="#f0f0f0")
        total_frame.pack(fill=tk.X, pady=10)
        
        total_label = tk.Label(total_frame, text=str(len(self.employees_data)),
                             font=("Arial", 24, "bold"), bg="#f0f0f0", fg="#27ae60")
        total_label.pack(pady=10)
        
        # توزيع الجنس (حسب الرقم الوطني)
        gender_frame = tk.LabelFrame(stats_frame, text="توزيع الجنس", 
                                   font=("Arial", 12, "bold"), bg="#f0f0f0")
        gender_frame.pack(fill=tk.X, pady=10)
        
        # حساب عدد الذكور والإناث
        males = 0
        females = 0
        for emp in self.employees_data:
            national_id = str(emp.get("الرقم الوطني", ""))
            if national_id.startswith("1"):
                males += 1
            elif national_id.startswith("2"):
                females += 1
        
        total = males + females
        male_percent = (males / total * 100) if total > 0 else 0
        female_percent = (females / total * 100) if total > 0 else 0
        
        # عرض توزيع الجنس
        gender_info = tk.Frame(gender_frame, bg="#f0f0f0")
        gender_info.pack(fill=tk.X, pady=10)
        
        tk.Label(gender_info, text=f"ذكور: {males} ({male_percent:.1f}%)",
               font=("Arial", 12), bg="#f0f0f0", fg="#3498db").pack(side=tk.LEFT, padx=20)
        
        tk.Label(gender_info, text=f"إناث: {females} ({female_percent:.1f}%)",
               font=("Arial", 12), bg="#f0f0f0", fg="#e83e8c").pack(side=tk.LEFT, padx=20)
        
        # توزيع أماكن العمل
        workplace_frame = tk.LabelFrame(stats_frame, text="توزيع أماكن العمل", 
                                      font=("Arial", 12, "bold"), bg="#f0f0f0")
        workplace_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # حساب عدد الموظفين في كل مكان عمل
        workplace_counts = {}
        for emp in self.employees_data:
            workplace = emp.get("مكان العمل الحالي", "غير محدد")
            if workplace in workplace_counts:
                workplace_counts[workplace] += 1
            else:
                workplace_counts[workplace] = 1
        
        # ترتيب أماكن العمل حسب عدد الموظفين
        sorted_workplaces = sorted(workplace_counts.items(), key=lambda x: x[1], reverse=True)
        
        # إنشاء إطار التمرير
        scroll_frame = tk.Frame(workplace_frame)
        scroll_frame.pack(fill=tk.BOTH, expand=True)
        
        # إنشاء شريط التمرير
        scrollbar = tk.Scrollbar(scroll_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # إنشاء قائمة أماكن العمل
        workplace_list = tk.Listbox(scroll_frame, font=("Arial", 12), 
                                  yscrollcommand=scrollbar.set, height=10)
        workplace_list.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # ربط شريط التمرير بالقائمة
        scrollbar.config(command=workplace_list.yview)
        
        # إضافة أماكن العمل للقائمة
        for workplace, count in sorted_workplaces:
            percent = (count / len(self.employees_data) * 100)
            workplace_list.insert(tk.END, f"{workplace}: {count} موظف ({percent:.1f}%)")
        
        # إنشاء إطار الأزرار
        buttons_frame = tk.Frame(stats_frame, bg="#f0f0f0")
        buttons_frame.pack(pady=20)
        
        # تم حذف زر معاينة الإحصائيات
        
        # زر تصدير الإحصائيات
        export_button = tk.Button(buttons_frame, text="📄 تصدير الإحصائيات",
                                command=lambda: self.handle_export_statistics(
                                    len(self.employees_data), males, females,
                                    male_percent, female_percent, sorted_workplaces),
                                bg="#27ae60", fg="white", font=("Arial", 12, "bold"),
                                padx=20, pady=10)
        export_button.pack(side=tk.LEFT, padx=10)

        # زر عرض مكان القالب والتعديل
        template_button = tk.Button(buttons_frame, text="📁 عرض مكان القالب",
                                  command=self.show_template_location_and_editor,
                                  bg="#3498db", fg="white", font=("Arial", 12, "bold"),
                                  padx=20, pady=10)
        template_button.pack(side=tk.LEFT, padx=10)
        
        # زر الإغلاق
        close_button = tk.Button(buttons_frame, text="إغلاق", 
                               command=stats_window.destroy,
                               bg="#3498db", fg="white", font=("Arial", 12, "bold"),
                               padx=20, pady=10)
        close_button.pack(side=tk.LEFT, padx=10)
    
    def preview_statistics(self, total_employees, males, females, male_percent, female_percent, workplace_data):
        """تم إلغاء وظيفة معاينة الإحصائيات"""
        # تصدير الإحصائيات مباشرة بدلاً من معاينتها
        self.handle_export_statistics(total_employees, males, females, male_percent, female_percent, workplace_data)
        return
        try:
            # تحديث شريط الحالة
            self.update_status("جاري إنشاء معاينة التقرير...")
            
            # إنشاء نافذة المعاينة
            preview_window = tk.Toplevel(self.root)
            preview_window.title("معاينة تقرير الإحصائيات")
            preview_window.geometry("900x700")
            preview_window.configure(bg="#f0f0f0")
            
            # إنشاء إطار الخيارات
            options_frame = tk.LabelFrame(preview_window, text="خيارات التقرير", bg="#f0f0f0", font=("Arial", 12, "bold"))
            options_frame.pack(fill="x", padx=20, pady=(20, 10))
            
            # خيارات العنوان
            title_frame = tk.Frame(options_frame, bg="#f0f0f0")
            title_frame.pack(fill="x", padx=10, pady=5)
            
            title_label = tk.Label(title_frame, text="عنوان التقرير:", bg="#f0f0f0", font=("Arial", 12))
            title_label.pack(side="left", padx=(0, 10))
            
            title_var = tk.StringVar(value="إحصائيات الموظفين")
            title_entry = tk.Entry(title_frame, textvariable=title_var, width=40, font=("Arial", 12))
            title_entry.pack(side="left", fill="x", expand=True)
            
            # خيارات الخط
            font_frame = tk.Frame(options_frame, bg="#f0f0f0")
            font_frame.pack(fill="x", padx=10, pady=5)
            
            font_label = tk.Label(font_frame, text="حجم الخط:", bg="#f0f0f0", font=("Arial", 12))
            font_label.pack(side="left", padx=(0, 10))
            
            font_size_var = tk.StringVar(value="متوسط")
            font_sizes = ["صغير", "متوسط", "كبير"]
            font_menu = ttk.Combobox(font_frame, textvariable=font_size_var, values=font_sizes, width=10)
            font_menu.pack(side="left", padx=(0, 20))
            
            # خيارات الألوان
            color_label = tk.Label(font_frame, text="لون العناوين:", bg="#f0f0f0", font=("Arial", 12))
            color_label.pack(side="left", padx=(0, 10))
            
            color_var = tk.StringVar(value="أزرق")
            colors = ["أزرق", "أخضر", "أحمر", "بنفسجي", "أسود"]
            color_menu = ttk.Combobox(font_frame, textvariable=color_var, values=colors, width=10)
            color_menu.pack(side="left")
            
            # خيارات المحتوى
            content_frame = tk.Frame(options_frame, bg="#f0f0f0")
            content_frame.pack(fill="x", padx=10, pady=5)
            
            # خيار إظهار توزيع الجنس
            show_gender_var = tk.BooleanVar(value=True)
            show_gender_check = tk.Checkbutton(content_frame, text="إظهار توزيع الجنس", variable=show_gender_var, bg="#f0f0f0", font=("Arial", 12))
            show_gender_check.pack(side="left", padx=(0, 20))
            
            # خيار إظهار توزيع أماكن العمل
            show_workplace_var = tk.BooleanVar(value=True)
            show_workplace_check = tk.Checkbutton(content_frame, text="إظهار توزيع أماكن العمل", variable=show_workplace_var, bg="#f0f0f0", font=("Arial", 12))
            show_workplace_check.pack(side="left", padx=(0, 20))
            
            # خيار إظهار الملاحظة
            show_note_var = tk.BooleanVar(value=True)
            show_note_check = tk.Checkbutton(content_frame, text="إظهار الملاحظة", variable=show_note_var, bg="#f0f0f0", font=("Arial", 12))
            show_note_check.pack(side="left")
            
            # زر تحديث المعاينة
            update_button = tk.Button(options_frame, text="تحديث المعاينة", 
                                   bg="#3498db", fg="white", font=("Arial", 12, "bold"),
                                   padx=15, pady=5)
            update_button.pack(pady=10)
            
            # إنشاء إطار التمرير
            preview_frame = tk.Frame(preview_window, bg="#ffffff")
            preview_canvas = tk.Canvas(preview_frame, bg="#ffffff")
            scrollbar = ttk.Scrollbar(preview_frame, orient="vertical", command=preview_canvas.yview)
            
            scrollable_frame = tk.Frame(preview_canvas, bg="#ffffff")
            scrollable_frame.bind(
                "<Configure>",
                lambda e: preview_canvas.configure(scrollregion=preview_canvas.bbox("all"))
            )
            
            preview_canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            preview_canvas.configure(yscrollcommand=scrollbar.set)
            
            preview_frame.pack(fill="both", expand=True, padx=20, pady=10)
            preview_canvas.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")
            
            # وظيفة تحديث المعاينة
            def update_preview():
                # حذف جميع العناصر الحالية
                for widget in scrollable_frame.winfo_children():
                    widget.destroy()
                
                # تحديد حجم الخط بناءً على الاختيار
                if font_size_var.get() == "صغير":
                    title_font_size = 16
                    subtitle_font_size = 12
                    text_font_size = 10
                elif font_size_var.get() == "كبير":
                    title_font_size = 22
                    subtitle_font_size = 18
                    text_font_size = 14
                else:  # متوسط
                    title_font_size = 18
                    subtitle_font_size = 14
                    text_font_size = 12
                
                # تحديد لون العناوين
                color_map = {
                    "أزرق": "#3498db",
                    "أخضر": "#27ae60",
                    "أحمر": "#e74c3c",
                    "بنفسجي": "#9b59b6",
                    "أسود": "#2c3e50"
                }
                title_color = color_map.get(color_var.get(), "#3498db")
                
                # إضافة العنوان
                title_label = tk.Label(scrollable_frame, text=title_var.get(), font=("Arial", title_font_size, "bold"), bg="#ffffff", fg=title_color)
                title_label.pack(pady=(20, 10))
                
                # إضافة التاريخ والوقت
                current_time = datetime.now().strftime("%Y/%m/%d %H:%M:%S")
                date_label = tk.Label(scrollable_frame, text=f"تاريخ التقرير: {current_time}", font=("Arial", text_font_size), bg="#ffffff")
                date_label.pack(pady=(0, 20))
                
                # إضافة خط أفقي
                separator = tk.Frame(scrollable_frame, height=2, width=700, bg="#e0e0e0")
                separator.pack(pady=10)
                
                # إجمالي عدد الموظفين
                total_frame = tk.Frame(scrollable_frame, bg="#ffffff")
                total_frame.pack(fill="x", padx=20, pady=10)
                
                total_title = tk.Label(total_frame, text="إجمالي عدد الموظفين", font=("Arial", subtitle_font_size, "bold"), bg="#ffffff", fg=title_color)
                total_title.pack(anchor="w")
                
                total_count = tk.Label(total_frame, text=f"العدد الكلي للموظفين: {total_employees}", font=("Arial", text_font_size), bg="#ffffff")
                total_count.pack(anchor="w", padx=20, pady=5)
                
                # إضافة خط أفقي
                separator = tk.Frame(scrollable_frame, height=2, width=700, bg="#e0e0e0")
                separator.pack(pady=10)
                
                # توزيع الجنس
                if show_gender_var.get():
                    gender_frame = tk.Frame(scrollable_frame, bg="#ffffff")
                    gender_frame.pack(fill="x", padx=20, pady=10)
                    
                    gender_title = tk.Label(gender_frame, text="توزيع الجنس (حسب الرقم الوطني)", font=("Arial", subtitle_font_size, "bold"), bg="#ffffff", fg=title_color)
                    gender_title.pack(anchor="w")
                    
                    gender_table_frame = tk.Frame(gender_frame, bg="#ffffff")
                    gender_table_frame.pack(fill="x", padx=20, pady=10)
                    
                    # إنشاء جدول توزيع الجنس
                    gender_headers = ["الفئة", "العدد", "النسبة المئوية"]
                    gender_data = [
                        ["ذكور", str(males), f"{male_percent:.1f}%"],
                        ["إناث", str(females), f"{female_percent:.1f}%"]
                    ]
                    
                    # إنشاء رؤوس الجدول
                    for i, header in enumerate(gender_headers):
                        header_label = tk.Label(gender_table_frame, text=header, font=("Arial", text_font_size, "bold"), bg="#e0e0e0", width=15, padx=5, pady=5)
                        header_label.grid(row=0, column=i, sticky="nsew")
                    
                    # إضافة بيانات الجدول
                    for i, row in enumerate(gender_data):
                        for j, cell in enumerate(row):
                            cell_label = tk.Label(gender_table_frame, text=cell, font=("Arial", text_font_size), bg="#ffffff", width=15, padx=5, pady=5)
                            cell_label.grid(row=i+1, column=j, sticky="nsew")
                    
                    # إضافة خط أفقي
                    separator = tk.Frame(scrollable_frame, height=2, width=700, bg="#e0e0e0")
                    separator.pack(pady=10)
                
                # توزيع أماكن العمل
                if show_workplace_var.get() and workplace_data and isinstance(workplace_data, list):
                    workplace_frame = tk.Frame(scrollable_frame, bg="#ffffff")
                    workplace_frame.pack(fill="x", padx=20, pady=10)
                    
                    workplace_title = tk.Label(workplace_frame, text="توزيع أماكن العمل", font=("Arial", subtitle_font_size, "bold"), bg="#ffffff", fg=title_color)
                    workplace_title.pack(anchor="w")
                    
                    workplace_table_frame = tk.Frame(workplace_frame, bg="#ffffff")
                    workplace_table_frame.pack(fill="x", padx=20, pady=10)
                    
                    # إنشاء جدول توزيع أماكن العمل
                    workplace_headers = ["مكان العمل", "عدد الموظفين", "النسبة المئوية"]
                    
                    # إنشاء رؤوس الجدول
                    for i, header in enumerate(workplace_headers):
                        header_label = tk.Label(workplace_table_frame, text=header, font=("Arial", text_font_size, "bold"), bg="#e0e0e0", width=15, padx=5, pady=5)
                        header_label.grid(row=0, column=i, sticky="nsew")
                    
                    # إضافة بيانات الجدول
                    for i, item in enumerate(workplace_data):
                        if isinstance(item, tuple) and len(item) == 2:
                            workplace, count = item
                            percent = (count / total_employees * 100) if total_employees > 0 else 0
                            
                            workplace_label = tk.Label(workplace_table_frame, text=str(workplace), font=("Arial", text_font_size), bg="#ffffff", width=15, padx=5, pady=5)
                            workplace_label.grid(row=i+1, column=0, sticky="nsew")
                            
                            count_label = tk.Label(workplace_table_frame, text=str(count), font=("Arial", text_font_size), bg="#ffffff", width=15, padx=5, pady=5)
                            count_label.grid(row=i+1, column=1, sticky="nsew")
                            
                            percent_label = tk.Label(workplace_table_frame, text=f"{percent:.1f}%", font=("Arial", text_font_size), bg="#ffffff", width=15, padx=5, pady=5)
                            percent_label.grid(row=i+1, column=2, sticky="nsew")
                    
                    # إضافة خط أفقي
                    separator = tk.Frame(scrollable_frame, height=2, width=700, bg="#e0e0e0")
                    separator.pack(pady=10)
                
                # إضافة ملاحظة في نهاية التقرير
                if show_note_var.get():
                    note_label = tk.Label(scrollable_frame, text="ملاحظة: تم إنشاء هذا التقرير بواسطة نظام إدارة الموظفين المبسط", font=("Arial", text_font_size - 2), bg="#ffffff")
                    note_label.pack(pady=20)
            
            # ربط وظيفة التحديث بزر التحديث
            update_button.config(command=update_preview)
            
            # تنفيذ التحديث الأولي
            update_preview()
            
            # إضافة أزرار التصدير والإغلاق
            buttons_frame = tk.Frame(preview_window, bg="#f0f0f0")
            buttons_frame.pack(pady=20)
            
            # وظيفة التصدير مع الخيارات المحددة
            def export_with_options():
                # تجميع الخيارات المحددة
                export_options = {
                    "title": title_var.get(),
                    "font_size": font_size_var.get(),
                    "title_color": color_var.get(),
                    "show_gender": show_gender_var.get(),
                    "show_workplace": show_workplace_var.get(),
                    "show_note": show_note_var.get()
                }
                
                # تنفيذ التصدير مع الخيارات
                self.handle_export_statistics_with_options(
                    total_employees, males, females, 
                    male_percent, female_percent, workplace_data,
                    export_options
                )
                
                # إغلاق نافذة المعاينة
                preview_window.destroy()
            
            # زر التصدير
            export_button = tk.Button(buttons_frame, text="تصدير", 
                                    command=export_with_options,
                                    bg="#27ae60", fg="white", font=("Arial", 12, "bold"),
                                    padx=20, pady=10)
            export_button.pack(side=tk.LEFT, padx=10)
            
            # زر الإغلاق
            close_button = tk.Button(buttons_frame, text="إغلاق", 
                                   command=preview_window.destroy,
                                   bg="#e74c3c", fg="white", font=("Arial", 12, "bold"),
                                   padx=20, pady=10)
            close_button.pack(side=tk.LEFT, padx=10)
            
            # تحديث شريط الحالة
            self.update_status("تم إنشاء معاينة التقرير")
            
        except Exception as e:
            # عرض رسالة خطأ
            messagebox.showerror("خطأ في المعاينة", f"حدث خطأ غير متوقع:\n{str(e)}")
            self.update_status("فشل في إنشاء معاينة التقرير")
            print(f"[خطأ] خطأ غير متوقع في المعاينة: {str(e)}")
            
    def handle_export_statistics_with_options(self, total_employees, males, females, male_percent, female_percent, workplace_data, options):
        """تم إلغاء وظيفة تصدير الإحصائيات مع الخيارات المخصصة"""
        # استخدام وظيفة التصدير العادية بدلاً من ذلك
        self.handle_export_statistics(total_employees, males, females, male_percent, female_percent, workplace_data)
        return
        try:
            # تحديث شريط الحالة
            self.update_status("جاري تصدير الإحصائيات...")
            
            # تنفيذ التصدير مع الخيارات
            result = self.export_statistics_to_word_with_options(
                total_employees, males, females, 
                male_percent, female_percent, workplace_data,
                options
            )
            
            # تحديث شريط الحالة
            if result:
                self.update_status("تم تصدير الإحصائيات بنجاح")
            else:
                self.update_status("فشل في تصدير الإحصائيات")
                
        except Exception as e:
            # عرض رسالة خطأ
            messagebox.showerror("خطأ في التصدير", f"حدث خطأ غير متوقع:\n{str(e)}")
            self.update_status("فشل في تصدير الإحصائيات")
            print(f"[خطأ] خطأ غير متوقع في التصدير: {str(e)}")
            
    def export_statistics_to_word_with_options(self, total_employees, males, females, male_percent, female_percent, workplace_data, options):
        """تصدير الإحصائيات إلى ملف Word مع الخيارات المخصصة"""
        try:
            # تحديد اسم الملف الافتراضي
            current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
            default_filename = f"إحصائيات_الموظفين_{current_time}"
            
            # فتح مربع حوار لاختيار مكان الحفظ
            file_path = filedialog.asksaveasfilename(
                defaultextension=".docx",
                filetypes=[("ملفات Word", "*.docx"), ("جميع الملفات", "*.*")],
                initialfile=default_filename,
                title="حفظ تقرير الإحصائيات"
            )
            
            if not file_path:
                print("[معلومات] تم إلغاء التصدير")
                return False
            
            # استخدام مكتبة python-docx إذا كانت متاحة
            if WORD_AVAILABLE:
                print("[جاري] استخدام تصدير Word...")
                return self.export_to_word_with_options(file_path, total_employees, males, females, male_percent, female_percent, workplace_data, options)
            else:
                print("[جاري] استخدام تصدير نصي...")
                return self.export_to_text_with_options(file_path, total_employees, males, females, male_percent, female_percent, workplace_data, options)
                
        except Exception as e:
            print(f"[خطأ] خطأ في تصدير الإحصائيات: {e}")
            raise
            
    def export_to_word_with_options(self, file_path, total_employees, males, females, male_percent, female_percent, workplace_data, options):
        """تصدير الإحصائيات إلى ملف Word مع الخيارات المخصصة"""
        try:
            print(f"[جاري] تصدير الإحصائيات إلى: {file_path}")
            
            # تحديد حجم الخط بناءً على الاختيار
            if options.get("font_size") == "صغير":
                title_font_size = 16
                subtitle_font_size = 14
                text_font_size = 11
            elif options.get("font_size") == "كبير":
                title_font_size = 20
                subtitle_font_size = 18
                text_font_size = 14
            else:  # متوسط
                title_font_size = 18
                subtitle_font_size = 16
                text_font_size = 12
            
            # تحديد لون العناوين
            color_map = {
                "أزرق": RGBColor(52, 152, 219),  # #3498db
                "أخضر": RGBColor(39, 174, 96),   # #27ae60
                "أحمر": RGBColor(231, 76, 60),   # #e74c3c
                "بنفسجي": RGBColor(155, 89, 182), # #9b59b6
                "أسود": RGBColor(44, 62, 80)     # #2c3e50
            }
            title_color = color_map.get(options.get("title_color", "أزرق"), RGBColor(52, 152, 219))
            
            # إنشاء مستند Word جديد
            doc = Document()
            
            # إعداد هوامش الصفحة
            sections = doc.sections
            for section in sections:
                section.top_margin = Cm(2)
                section.bottom_margin = Cm(2)
                section.left_margin = Cm(2)
                section.right_margin = Cm(2)
            
            # إضافة العنوان
            title = doc.add_heading(options.get("title", "إحصائيات الموظفين"), level=0)
            # تعيين اتجاه الكتابة من اليمين إلى اليسار للعنوان
            for run in title.runs:
                try:
                    run.rtl = True
                    run.font.size = Pt(title_font_size)
                    run.font.color.rgb = title_color
                except:
                    pass
            
            # إضافة التاريخ والوقت
            current_time = datetime.now().strftime("%Y/%m/%d %H:%M:%S")
            date_paragraph = doc.add_paragraph(f"تاريخ التقرير: {current_time}")
            date_paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
            for run in date_paragraph.runs:
                try:
                    run.rtl = True
                    run.font.size = Pt(text_font_size - 2)
                except:
                    pass
            
            # إضافة مسافة
            spacer = doc.add_paragraph()
            
            # إجمالي عدد الموظفين
            total_heading = doc.add_heading('إجمالي عدد الموظفين', level=1)
            for run in total_heading.runs:
                try:
                    run.rtl = True
                    run.font.size = Pt(subtitle_font_size)
                    run.font.color.rgb = title_color
                except:
                    pass
            
            total_paragraph = doc.add_paragraph(f"العدد الكلي للموظفين: {total_employees}")
            for run in total_paragraph.runs:
                try:
                    run.rtl = True
                    run.font.size = Pt(text_font_size)
                except:
                    pass
            
            # إضافة مسافة
            spacer = doc.add_paragraph()
            
            # توزيع الجنس
            if options.get("show_gender", True):
                gender_heading = doc.add_heading('توزيع الجنس (حسب الرقم الوطني)', level=1)
                for run in gender_heading.runs:
                    try:
                        run.rtl = True
                        run.font.size = Pt(subtitle_font_size)
                        run.font.color.rgb = title_color
                    except:
                        pass
                
                # إنشاء جدول توزيع الجنس
                gender_table = doc.add_table(rows=1, cols=3)
                gender_table.style = 'Table Grid'
                try:
                    gender_table.bidi = True
                except:
                    pass
                
                # إضافة رؤوس الجدول
                header_cells = gender_table.rows[0].cells
                header_cells[2].text = 'الفئة'
                header_cells[1].text = 'العدد'
                header_cells[0].text = 'النسبة المئوية'
                
                # تعيين اتجاه النص في خلايا الرأس
                for cell in header_cells:
                    for paragraph in cell.paragraphs:
                        for run in paragraph.runs:
                            try:
                                run.rtl = True
                                run.font.size = Pt(text_font_size)
                                run.bold = True
                            except:
                                pass
                
                # إضافة بيانات الذكور
                male_cells = gender_table.add_row().cells
                male_cells[2].text = 'ذكور'
                male_cells[1].text = str(males)
                male_cells[0].text = f'{male_percent:.1f}%'
                
                # إضافة بيانات الإناث
                female_cells = gender_table.add_row().cells
                female_cells[2].text = 'إناث'
                female_cells[1].text = str(females)
                female_cells[0].text = f'{female_percent:.1f}%'
                
                # تعيين اتجاه النص في خلايا البيانات
                for row in gender_table.rows[1:]:
                    for cell in row.cells:
                        for paragraph in cell.paragraphs:
                            for run in paragraph.runs:
                                try:
                                    run.rtl = True
                                    run.font.size = Pt(text_font_size)
                                except:
                                    pass
                
                # إضافة مسافة
                spacer = doc.add_paragraph()
            
            # توزيع أماكن العمل
            if options.get("show_workplace", True) and workplace_data and isinstance(workplace_data, list):
                workplace_heading = doc.add_heading('توزيع أماكن العمل', level=1)
                for run in workplace_heading.runs:
                    try:
                        run.rtl = True
                        run.font.size = Pt(subtitle_font_size)
                        run.font.color.rgb = title_color
                    except:
                        pass
                
                # إنشاء جدول توزيع أماكن العمل
                workplace_table = doc.add_table(rows=1, cols=3)
                workplace_table.style = 'Table Grid'
                try:
                    workplace_table.bidi = True
                except:
                    pass
                
                # إضافة رؤوس الجدول
                header_cells = workplace_table.rows[0].cells
                header_cells[2].text = 'مكان العمل'
                header_cells[1].text = 'عدد الموظفين'
                header_cells[0].text = 'النسبة المئوية'
                
                # تعيين اتجاه النص في خلايا الرأس
                for cell in header_cells:
                    for paragraph in cell.paragraphs:
                        for run in paragraph.runs:
                            try:
                                run.rtl = True
                                run.font.size = Pt(text_font_size)
                                run.bold = True
                            except:
                                pass
                
                # إضافة بيانات أماكن العمل
                for item in workplace_data:
                    if isinstance(item, tuple) and len(item) == 2:
                        workplace, count = item
                        percent = (count / total_employees * 100) if total_employees > 0 else 0
                        
                        row_cells = workplace_table.add_row().cells
                        row_cells[2].text = str(workplace)
                        row_cells[1].text = str(count)
                        row_cells[0].text = f'{percent:.1f}%'
                        
                        # تعيين اتجاه النص في خلايا الصف
                        for cell in row_cells:
                            for paragraph in cell.paragraphs:
                                for run in paragraph.runs:
                                    try:
                                        run.rtl = True
                                        run.font.size = Pt(text_font_size)
                                    except:
                                        pass
            
            # إضافة ملاحظة في نهاية المستند
            if options.get("show_note", True):
                spacer = doc.add_paragraph()
                
                note = doc.add_paragraph('ملاحظة: تم إنشاء هذا التقرير بواسطة نظام إدارة الموظفين المبسط')
                note.alignment = WD_ALIGN_PARAGRAPH.CENTER
                for run in note.runs:
                    try:
                        run.rtl = True
                        run.font.size = Pt(text_font_size - 2)
                    except:
                        pass
            
            # حفظ المستند
            doc.save(file_path)
            print(f"[تم] تصدير الإحصائيات إلى ملف Word: {file_path}")
            
            # فتح الملف بعد التصدير
            os.startfile(file_path)
            
            return True
            
        except Exception as e:
            print(f"[خطأ] خطأ في تصدير الإحصائيات إلى Word: {e}")
            
            # محاولة التصدير إلى مسار بديل
            try:
                alt_path = os.path.join(os.path.expanduser("~"), "Desktop", os.path.basename(file_path))
                print(f"[محاولة] تصدير إلى المسار البديل: {alt_path}")
                
                doc.save(alt_path)
                print(f"[تم] تصدير الإحصائيات إلى المسار البديل: {alt_path}")
                
                # فتح الملف بعد التصدير
                os.startfile(alt_path)
                
                return True
                
            except Exception as alt_error:
                print(f"[خطأ] فشل أيضاً في التصدير إلى المسار البديل: {str(alt_error)}")
            
            raise  # إعادة رفع الاستثناء
            
    def export_to_text_with_options(self, file_path, total_employees, males, females, male_percent, female_percent, workplace_data, options):
        """تصدير الإحصائيات إلى ملف نصي مع الخيارات المخصصة"""
        try:
            # تغيير امتداد الملف إلى .txt
            if file_path.lower().endswith('.docx'):
                file_path = file_path[:-5] + '.txt'
            
            print(f"[جاري] تصدير الإحصائيات إلى ملف نصي: {file_path}")
            
            with open(file_path, 'w', encoding='utf-8') as f:
                # إضافة علامة ترتيب النص من اليمين إلى اليسار (RLM)
                rlm = '\u200F'
                
                # إضافة العنوان
                f.write(f"{rlm}{options.get('title', 'إحصائيات الموظفين')}\n")
                f.write(f"{rlm}{'=' * 50}\n\n")
                
                # إضافة التاريخ والوقت
                current_time = datetime.now().strftime("%Y/%m/%d %H:%M:%S")
                f.write(f"{rlm}تاريخ التقرير: {current_time}\n\n")
                
                # إجمالي عدد الموظفين
                f.write(f"{rlm}إجمالي عدد الموظفين\n")
                f.write(f"{rlm}{'-' * 30}\n")
                f.write(f"{rlm}العدد الكلي للموظفين: {total_employees}\n\n")
                
                # توزيع الجنس
                if options.get("show_gender", True):
                    f.write(f"{rlm}توزيع الجنس (حسب الرقم الوطني)\n")
                    f.write(f"{rlm}{'-' * 30}\n")
                    f.write(f"{rlm}{'الفئة':<15}{'العدد':<15}{'النسبة المئوية':<15}\n")
                    f.write(f"{rlm}{'-' * 45}\n")
                    f.write(f"{rlm}{'ذكور':<15}{males:<15}{male_percent:.1f}%\n")
                    f.write(f"{rlm}{'إناث':<15}{females:<15}{female_percent:.1f}%\n\n")
                
                # توزيع أماكن العمل
                if options.get("show_workplace", True) and workplace_data and isinstance(workplace_data, list):
                    f.write(f"{rlm}توزيع أماكن العمل\n")
                    f.write(f"{rlm}{'-' * 30}\n")
                    f.write(f"{rlm}{'مكان العمل':<20}{'العدد':<15}{'النسبة المئوية':<15}\n")
                    f.write(f"{rlm}{'-' * 50}\n")
                    
                    for item in workplace_data:
                        if isinstance(item, tuple) and len(item) == 2:
                            workplace, count = item
                            percent = (count / total_employees * 100) if total_employees > 0 else 0
                            f.write(f"{rlm}{workplace:<20}{count:<15}{percent:.1f}%\n")
                    
                    f.write("\n")
                
                # إضافة ملاحظة في نهاية الملف
                if options.get("show_note", True):
                    f.write(f"\n{rlm}ملاحظة: تم إنشاء هذا التقرير بواسطة نظام إدارة الموظفين المبسط\n")
            
            print(f"[تم] تصدير الإحصائيات إلى ملف نصي: {file_path}")
            
            # فتح الملف بعد التصدير
            os.startfile(file_path)
            
            return True
            
        except Exception as e:
            print(f"[خطأ] خطأ في تصدير الإحصائيات إلى ملف نصي: {e}")
            
            # محاولة التصدير إلى مسار بديل
            try:
                alt_path = os.path.join(os.path.expanduser("~"), "Desktop", os.path.basename(file_path))
                print(f"[محاولة] تصدير إلى المسار البديل: {alt_path}")
                
                with open(alt_path, 'w', encoding='utf-8') as f:
                    f.write(f"إحصائيات الموظفين\n")
                    f.write(f"==================\n\n")
                    f.write(f"العدد الكلي للموظفين: {total_employees}\n\n")
                    f.write(f"ذكور: {males} ({male_percent:.1f}%)\n")
                    f.write(f"إناث: {females} ({female_percent:.1f}%)\n\n")
                
                print(f"[تم] تصدير الإحصائيات إلى المسار البديل: {alt_path}")
                
                # فتح الملف بعد التصدير
                os.startfile(alt_path)
                
                return True
                
            except Exception as alt_error:
                print(f"[خطأ] فشل أيضاً في التصدير إلى المسار البديل: {str(alt_error)}")
            
            raise  # إعادة رفع الاستثناء
    
    def handle_export_statistics(self, total_employees, males, females, male_percent, female_percent, workplace_data):
        """معالجة تصدير الإحصائيات إلى Word مباشرة"""
        try:
            # تحديث شريط الحالة
            self.update_status("جاري تصدير الإحصائيات إلى Word...")

            # تنفيذ التصدير إلى Word مباشرة
            result = self.export_statistics_to_word_direct(
                total_employees, males, females,
                male_percent, female_percent, workplace_data
            )

            # تحديث شريط الحالة
            if result:
                self.update_status("تم تصدير الإحصائيات بنجاح")
            else:
                self.update_status("فشل في تصدير الإحصائيات")

        except Exception as e:
            # عرض رسالة خطأ
            messagebox.showerror("خطأ في التصدير", f"حدث خطأ غير متوقع:\n{str(e)}")
            self.update_status("فشل في تصدير الإحصائيات")
            print(f"[خطأ] خطأ غير متوقع في التصدير: {str(e)}")

    def get_last_employee_id(self, form_vars):
        """الحصول على آخر رقم وظيفي من ملف Excel وتعيين الرقم التالي"""
        try:
            if not EXCEL_AVAILABLE:
                messagebox.showwarning("تحذير", "مكتبة Excel غير متاحة")
                return

            # قراءة ملف Excel للحصول على آخر رقم وظيفي
            if not os.path.exists(self.excel_file):
                messagebox.showinfo("معلومات", "ملف البيانات غير موجود. سيتم البدء من الرقم 1")
                form_vars["الرقم الوظيفي"].set("1")
                return

            wb = load_workbook(self.excel_file)

            # التحقق من وجود الورقة المطلوبة
            if self.sheet_name in wb.sheetnames:
                ws = wb[self.sheet_name]
            else:
                ws = wb.active

            max_id = 0

            # البحث عن أكبر رقم وظيفي
            for row in ws.iter_rows(min_row=2, values_only=True):
                if row and row[0]:  # إذا كان هناك رقم وظيفي
                    try:
                        emp_id = int(str(row[0]).strip())
                        if emp_id > max_id:
                            max_id = emp_id
                    except (ValueError, TypeError):
                        continue

            wb.close()

            # تعيين الرقم التالي
            next_id = max_id + 1
            form_vars["الرقم الوظيفي"].set(str(next_id))

            # عرض رسالة للمستخدم
            messagebox.showinfo("تم", f"آخر رقم وظيفي: {max_id}\nالرقم التالي: {next_id}")
            print(f"[تم] تم تعيين الرقم الوظيفي التالي: {next_id}")

        except Exception as e:
            print(f"[خطأ] خطأ في الحصول على آخر رقم وظيفي: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ في الحصول على آخر رقم وظيفي:\n{str(e)}")

    def export_statistics_to_word_direct(self, total_employees, males, females, male_percent, female_percent, workplace_data):
        """تصدير الإحصائيات إلى Word مباشرة باستخدام القالب المخصص"""
        try:
            # التحقق من توفر مكتبة Word
            if not WORD_AVAILABLE:
                messagebox.showerror(
                    "خطأ",
                    "مكتبة Word غير متاحة!\n\n"
                    "لتمكين تصدير ملفات Word، قم بتشغيل ملف:\n"
                    "'تثبيت_مكتبة_Word.bat'"
                )
                return False

            # تحديد اسم الملف الافتراضي
            current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
            default_filename = f"إحصائيات_الموظفين_{current_time}"

            # فتح مربع حوار لاختيار مكان حفظ الملف
            file_path = filedialog.asksaveasfilename(
                defaultextension=".docx",
                filetypes=[("ملفات Word", "*.docx")],
                initialfile=default_filename,
                title="حفظ تقرير الإحصائيات"
            )

            if not file_path:
                print("[معلومات] تم إلغاء التصدير")
                return False

            print(f"[جاري] تصدير الإحصائيات إلى Word: {file_path}")

            # تحميل القالب المخصص
            template_content = self.load_statistics_template()

            # إعداد البيانات للاستبدال
            current_datetime = datetime.now()

            # تحضير بيانات أماكن العمل
            workplace_text = ""
            if workplace_data and isinstance(workplace_data, list):
                for item in workplace_data:
                    if isinstance(item, tuple) and len(item) == 2:
                        workplace, count = item
                        percent = (count / total_employees * 100) if total_employees > 0 else 0
                        workplace_text += f'{workplace}: {count} موظف ({percent:.1f}%)\n'

            # استبدال المتغيرات في القالب
            content = template_content.replace('{TITLE}', 'إحصائيات الموظفين')
            content = content.replace('{DATE}', current_datetime.strftime("%Y/%m/%d"))
            content = content.replace('{TIME}', current_datetime.strftime("%H:%M:%S"))
            content = content.replace('{TOTAL_EMPLOYEES}', str(total_employees))
            content = content.replace('{MALES}', str(males))
            content = content.replace('{FEMALES}', str(females))
            content = content.replace('{MALE_PERCENT}', f'{male_percent:.1f}')
            content = content.replace('{FEMALE_PERCENT}', f'{female_percent:.1f}')
            content = content.replace('{WORKPLACE_DATA}', workplace_text.strip())

            # إنشاء مستند Word
            doc = Document()

            # إعداد اتجاه النص من اليمين إلى اليسار
            sections = doc.sections
            for section in sections:
                section.top_margin = Cm(2)
                section.bottom_margin = Cm(2)
                section.left_margin = Cm(2)
                section.right_margin = Cm(2)

            # تقسيم المحتوى إلى أسطر وإضافتها للمستند مع تحسين التنسيق
            lines = content.split('\n')
            for line in lines:
                if line.strip():
                    if line.startswith('╔') or line.startswith('╚'):
                        # إطار العنوان الرئيسي
                        para = doc.add_paragraph(line)
                        para.alignment = WD_ALIGN_PARAGRAPH.CENTER
                        # تطبيق خط مميز للإطار
                        for run in para.runs:
                            run.font.name = 'Courier New'
                            run.font.size = Pt(12)
                    elif 'إحصائيات الموظفين' in line and '║' in line:
                        # العنوان الرئيسي داخل الإطار
                        heading = doc.add_heading('إحصائيات الموظفين', level=0)
                        heading.alignment = WD_ALIGN_PARAGRAPH.CENTER
                        for run in heading.runs:
                            run.font.size = Pt(18)
                            run.font.bold = True
                    elif line.startswith('═══'):
                        # خطوط الفصل الرئيسية
                        para = doc.add_paragraph(line)
                        para.alignment = WD_ALIGN_PARAGRAPH.CENTER
                        for run in para.runs:
                            run.font.name = 'Courier New'
                            run.font.size = Pt(10)
                    elif line.startswith('📊') or line.startswith('👨‍👩‍👧‍👦') or line.startswith('🏢') or line.startswith('📋'):
                        # عناوين الأقسام الرئيسية
                        heading = doc.add_heading(line, level=1)
                        heading.alignment = WD_ALIGN_PARAGRAPH.RIGHT
                        for run in heading.runs:
                            run.font.size = Pt(14)
                            run.font.bold = True
                            run.rtl = True
                    elif line.startswith('📅') or line.startswith('🕐'):
                        # معلومات التاريخ والوقت
                        para = doc.add_paragraph(line)
                        para.alignment = WD_ALIGN_PARAGRAPH.CENTER
                        for run in para.runs:
                            run.font.size = Pt(11)
                            run.rtl = True
                    elif line.startswith('👥') or line.startswith('👨') or line.startswith('👩'):
                        # بيانات الموظفين
                        para = doc.add_paragraph(line)
                        para.alignment = WD_ALIGN_PARAGRAPH.RIGHT
                        for run in para.runs:
                            run.font.size = Pt(12)
                            run.rtl = True
                    elif line.startswith('✅'):
                        # نقاط المعلومات الإضافية
                        para = doc.add_paragraph(line)
                        para.alignment = WD_ALIGN_PARAGRAPH.RIGHT
                        for run in para.runs:
                            run.font.size = Pt(10)
                            run.rtl = True
                    elif line.startswith('🔚'):
                        # نهاية التقرير
                        para = doc.add_paragraph(line)
                        para.alignment = WD_ALIGN_PARAGRAPH.CENTER
                        for run in para.runs:
                            run.font.size = Pt(12)
                            run.font.bold = True
                            run.rtl = True
                    else:
                        # نص عادي
                        para = doc.add_paragraph(line)
                        para.alignment = WD_ALIGN_PARAGRAPH.RIGHT
                        for run in para.runs:
                            run.rtl = True
                else:
                    # سطر فارغ
                    doc.add_paragraph()

            # حفظ المستند
            doc.save(file_path)
            print(f"[تم] تصدير الإحصائيات إلى Word: {file_path}")

            # عرض رسالة نجاح
            messagebox.showinfo("تم بنجاح", f"تم تصدير الإحصائيات إلى ملف Word:\n{file_path}")

            # سؤال المستخدم إذا كان يريد فتح الملف
            if messagebox.askyesno("فتح الملف", "هل ترغب في فتح الملف الآن؟"):
                try:
                    os.startfile(file_path)
                except Exception as e:
                    print(f"[خطأ] خطأ في فتح الملف: {str(e)}")
                    messagebox.showwarning("تنبيه", f"لا يمكن فتح الملف تلقائياً.\nالملف موجود في: {file_path}")

            return True

        except Exception as e:
            print(f"[خطأ] خطأ في تصدير الإحصائيات إلى Word: {e}")
            messagebox.showerror("خطأ في التصدير", f"حدث خطأ أثناء تصدير الإحصائيات:\n{str(e)}")
            return False

    def export_statistics_to_word(self, total_employees, males, females, male_percent, female_percent, workplace_data):
        """تصدير الإحصائيات إلى ملف Word فقط"""
        # استخدام الوظيفة المباشرة للتصدير إلى Word
        return self.export_statistics_to_word_direct(total_employees, males, females, male_percent, female_percent, workplace_data)
    
    def export_to_word_file(self, file_path, total_employees, males, females, 
                           male_percent, female_percent, workplace_data):
        """تصدير الإحصائيات إلى ملف Word"""
        if not WORD_AVAILABLE:
            raise ImportError("مكتبة python-docx غير متاحة")
            
        try:
            print("[جاري] بدء إنشاء مستند Word...")
            # إنشاء مستند Word جديد
            doc = Document()
            
            print("[جاري] إضافة العنوان...")
            # إعداد العنوان
            title = doc.add_heading('إحصائيات الموظفين', level=0)
            title.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # تعيين اتجاه الكتابة من اليمين إلى اليسار للعنوان
            # العنوان هو كائن من نوع _ParagraphStyle وليس له خاصية paragraphs
            # لذلك نتعامل معه مباشرة
            # نستخدم فقط الخصائص المدعومة
            for run in title.runs:
                try:
                    run.rtl = True  # تعيين اتجاه النص من اليمين إلى اليسار
                except:
                    pass  # تجاهل الخطأ إذا كانت الخاصية غير مدعومة
            
            # إضافة التاريخ والوقت
            current_time = datetime.now().strftime("%Y/%m/%d %H:%M:%S")
            date_paragraph = doc.add_paragraph(f'تاريخ التقرير: {current_time}')
            date_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            # نستخدم فقط الخصائص المدعومة
            for run in date_paragraph.runs:
                try:
                    run.rtl = True  # تعيين اتجاه النص من اليمين إلى اليسار
                except:
                    pass  # تجاهل الخطأ إذا كانت الخاصية غير مدعومة
            
            # إضافة خط أفقي
            separator = doc.add_paragraph('_' * 50)
            separator.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            print("[جاري] إضافة إجمالي عدد الموظفين...")
            # إجمالي عدد الموظفين
            heading = doc.add_heading('إجمالي عدد الموظفين', level=1)
            # تعيين اتجاه الكتابة من اليمين إلى اليسار للعنوان
            for run in heading.runs:
                try:
                    run.rtl = True
                except:
                    pass
            
            # إضافة عدد الموظفين
            emp_count = doc.add_paragraph(f'العدد الكلي للموظفين: {total_employees}')
            for run in emp_count.runs:
                try:
                    run.rtl = True
                except:
                    pass
            
            print("[جاري] إضافة توزيع الجنس...")
            # توزيع الجنس
            gender_heading = doc.add_heading('توزيع الجنس (حسب الرقم الوطني)', level=1)
            # تعيين اتجاه الكتابة من اليمين إلى اليسار للعنوان
            for run in gender_heading.runs:
                try:
                    run.rtl = True
                except:
                    pass
            
            # إنشاء جدول توزيع الجنس
            gender_table = doc.add_table(rows=1, cols=3)
            gender_table.style = 'Table Grid'
            # نحاول تعيين اتجاه الجدول من اليمين إلى اليسار إذا كانت الخاصية مدعومة
            try:
                gender_table.bidi = True
            except:
                pass
            
            # تعيين اتجاه الجدول من اليمين إلى اليسار (طريقة بديلة)
            try:
                for row in gender_table.rows:
                    for cell in row.cells:
                        for paragraph in cell.paragraphs:
                            paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
                            paragraph.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.RIGHT
            except:
                pass
            
            # إضافة رؤوس الجدول
            header_cells = gender_table.rows[0].cells
            # ترتيب الخلايا من اليمين إلى اليسار (تغيير الترتيب)
            header_cells[0].text = 'الفئة'
            header_cells[1].text = 'العدد'
            header_cells[2].text = 'النسبة المئوية'
            
            # تعيين اتجاه النص في خلايا الرأس
            for cell in header_cells:
                for paragraph in cell.paragraphs:
                    for run in paragraph.runs:
                        try:
                            run.rtl = True
                        except:
                            pass
            
            # إضافة بيانات الذكور
            row_cells = gender_table.add_row().cells
            # ترتيب الخلايا من اليمين إلى اليسار (تغيير الترتيب)
            row_cells[0].text = 'ذكور'
            row_cells[1].text = str(males)
            row_cells[2].text = f'{male_percent:.1f}%'
            
            # تعيين اتجاه النص في خلايا الصف
            for cell in row_cells:
                cell.paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.RIGHT
                for paragraph in cell.paragraphs:
                    paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
                    for run in paragraph.runs:
                        try:
                            run.rtl = True
                            run.font.rtl = True
                        except:
                            pass
            
            # إضافة بيانات الإناث
            row_cells = gender_table.add_row().cells
            # ترتيب الخلايا من اليمين إلى اليسار (تغيير الترتيب)
            row_cells[0].text = 'إناث'
            row_cells[1].text = str(females)
            row_cells[2].text = f'{female_percent:.1f}%'
            
            # تعيين اتجاه النص في خلايا الصف
            for cell in row_cells:
                cell.paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.RIGHT
                for paragraph in cell.paragraphs:
                    paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
                    for run in paragraph.runs:
                        try:
                            run.rtl = True
                            run.font.rtl = True
                        except:
                            pass
            
            # إضافة مسافة
            spacer = doc.add_paragraph()
            
            print("[جاري] إضافة توزيع أماكن العمل...")
            # توزيع أماكن العمل
            workplace_heading = doc.add_heading('توزيع أماكن العمل', level=1)
            # تعيين اتجاه الكتابة من اليمين إلى اليسار للعنوان
            for run in workplace_heading.runs:
                try:
                    run.rtl = True
                except:
                    pass
            
            # إنشاء جدول توزيع أماكن العمل
            workplace_table = doc.add_table(rows=1, cols=3)
            workplace_table.style = 'Table Grid'
            # نحاول تعيين اتجاه الجدول من اليمين إلى اليسار إذا كانت الخاصية مدعومة
            try:
                workplace_table.bidi = True
            except:
                pass
            
            # تعيين اتجاه الجدول من اليمين إلى اليسار (طريقة بديلة)
            try:
                for row in workplace_table.rows:
                    for cell in row.cells:
                        for paragraph in cell.paragraphs:
                            paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
                            paragraph.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.RIGHT
            except:
                pass
            
            # إضافة رؤوس الجدول
            header_cells = workplace_table.rows[0].cells
            # ترتيب الخلايا من اليمين إلى اليسار (تغيير الترتيب)
            header_cells[0].text = 'مكان العمل'
            header_cells[1].text = 'عدد الموظفين'
            header_cells[2].text = 'النسبة المئوية'
            
            # تعيين اتجاه النص في خلايا الرأس
            for cell in header_cells:
                for paragraph in cell.paragraphs:
                    for run in paragraph.runs:
                        try:
                            run.rtl = True
                        except:
                            pass
            
            # إضافة بيانات أماكن العمل
            if workplace_data and isinstance(workplace_data, list):
                for item in workplace_data:
                    if isinstance(item, tuple) and len(item) == 2:
                        workplace, count = item
                        percent = (count / total_employees * 100) if total_employees > 0 else 0
                        row_cells = workplace_table.add_row().cells
                        # ترتيب الخلايا من اليمين إلى اليسار (تغيير الترتيب)
                        row_cells[0].text = str(workplace)
                        row_cells[1].text = str(count)
                        row_cells[2].text = f'{percent:.1f}%'
                        
                        # تعيين اتجاه النص في خلايا الصف
                        for cell in row_cells:
                            cell.paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.RIGHT
                            for paragraph in cell.paragraphs:
                                paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
                                for run in paragraph.runs:
                                    try:
                                        run.rtl = True
                                        run.font.rtl = True
                                    except:
                                        pass
            
            # إضافة ملاحظة في نهاية المستند
            spacer = doc.add_paragraph()
            
            note = doc.add_paragraph('ملاحظة: تم إنشاء هذا التقرير بواسطة نظام إدارة الموظفين المبسط')
            note.alignment = WD_ALIGN_PARAGRAPH.CENTER
            for run in note.runs:
                try:
                    run.rtl = True
                except:
                    pass
            
            print(f"[جاري] حفظ المستند إلى: {file_path}")
            # حفظ المستند
            doc.save(file_path)
            
            print(f"[تم] تصدير الإحصائيات إلى ملف Word: {file_path}")
            return True
            
        except Exception as e:
            print(f"[خطأ] خطأ في تصدير الإحصائيات إلى ملف Word: {str(e)}")
            
            # عرض رسالة خطأ للمستخدم
            messagebox.showerror(
                "خطأ في التصدير إلى Word",
                f"حدث خطأ أثناء تصدير ملف Word:\n{str(e)}\n\n"
                "يرجى التأكد من:\n"
                "1. تثبيت مكتبة python-docx\n"
                "2. صحة مسار الحفظ\n"
                "3. عدم فتح الملف في برنامج آخر"
            )
            raise  # إعادة رفع الاستثناء الأصلي
    

            
    def increase_font_size(self):
        """تكبير حجم الخط في الواجهة"""
        # تحديد الحجم التالي الأكبر
        if self.current_font_size == "small":
            next_size = "medium"
            size_name = "متوسط"
        elif self.current_font_size == "medium":
            next_size = "large"
            size_name = "كبير"
        else:  # large - لا يمكن التكبير أكثر
            next_size = "large"
            size_name = "كبير (الحد الأقصى)"
            self.update_status("تم الوصول إلى الحد الأقصى لحجم الخط")
            return
        
        # تحديث حجم الخط الحالي
        self.current_font_size = next_size
        
        # عرض رسالة للمستخدم
        self.update_status(f"تم تكبير الخط إلى: {size_name}")
        
        # تطبيق التغييرات
        self.apply_font_size()
        
        # حفظ الإعدادات
        self.save_font_size_settings()
    
    def decrease_font_size(self):
        """تصغير حجم الخط في الواجهة"""
        # تحديد الحجم التالي الأصغر
        if self.current_font_size == "large":
            next_size = "medium"
            size_name = "متوسط"
        elif self.current_font_size == "medium":
            next_size = "small"
            size_name = "صغير"
        else:  # small - لا يمكن التصغير أكثر
            next_size = "small"
            size_name = "صغير (الحد الأدنى)"
            self.update_status("تم الوصول إلى الحد الأدنى لحجم الخط")
            return
        
        # تحديث حجم الخط الحالي
        self.current_font_size = next_size
        
        # عرض رسالة للمستخدم
        self.update_status(f"تم تصغير الخط إلى: {size_name}")
        
        # تطبيق التغييرات
        self.apply_font_size()
        
        # حفظ الإعدادات
        self.save_font_size_settings()
    
    def save_font_size_settings(self):
        """حفظ إعدادات حجم الخط"""
        if hasattr(self, 'config'):
            if "ui_settings" not in self.config:
                self.config["ui_settings"] = {}
            self.config["ui_settings"]["font_size"] = self.current_font_size
            try:
                with open(self.config_file, 'w', encoding='utf-8') as f:
                    json.dump(self.config, f, ensure_ascii=False, indent=4)
                print(f"[تم] حفظ إعدادات حجم الخط: {self.current_font_size}")
            except Exception as e:
                print(f"[خطأ] فشل في حفظ إعدادات حجم الخط: {e}")
    
    def apply_font_size(self):
        """تطبيق حجم الخط ونوعه على عناصر الواجهة"""
        try:
            # الحصول على إعدادات الخط الحالية
            font_settings = self.font_sizes[self.current_font_size]
            
            # تطبيق حجم الخط ونوعه على الجدول
            style = ttk.Style()
            style.configure("Treeview", font=(self.current_font_family, font_settings["table"]))
            style.configure("Treeview.Heading", font=(self.current_font_family, font_settings["table"], "bold"))
            
            # تطبيق حجم الخط ونوعه على الأزرار
            for widget in self.root.winfo_children():
                self.update_font_recursive(widget, font_settings)
            
            # تحديث شريط الحالة
            if hasattr(self, 'status_label'):
                self.status_label.config(font=(self.current_font_family, font_settings["label"]))
            
            print(f"[تم] تطبيق حجم الخط: {self.current_font_size}")
            
        except Exception as e:
            print(f"[خطأ] فشل في تطبيق حجم الخط: {e}")
    
    def update_font_recursive(self, widget, font_settings):
        """تحديث حجم الخط ونوعه لجميع العناصر بشكل متكرر"""
        try:
            # تحديث الخط حسب نوع العنصر
            if isinstance(widget, tk.Button):
                widget.config(font=(self.current_font_family, font_settings["button"]))
            elif isinstance(widget, tk.Label):
                # التحقق مما إذا كان العنصر عنواناً
                current_font = widget.cget("font")
                if isinstance(current_font, str):
                    font_obj = font.Font(font=current_font)
                    if font_obj.actual()["size"] >= 14:
                        widget.config(font=(self.current_font_family, font_settings["title"], "bold"))
                    else:
                        widget.config(font=(self.current_font_family, font_settings["label"]))
                else:
                    widget.config(font=(self.current_font_family, font_settings["label"]))
            elif isinstance(widget, tk.Entry) or isinstance(widget, ttk.Combobox):
                widget.config(font=(self.current_font_family, font_settings["entry"]))
            elif isinstance(widget, ttk.Treeview):
                # تم التعامل معه في apply_font_size
                pass
            
            # التكرار على العناصر الفرعية
            for child in widget.winfo_children():
                self.update_font_recursive(child, font_settings)
                
        except Exception as e:
            # تجاهل الأخطاء للعناصر التي لا تدعم تغيير الخط
            pass
            
    def apply_dark_mode(self):
        """تطبيق الوضع الداكن على واجهة المستخدم"""
        try:
            # الحصول على مخطط الألوان المناسب
            colors = self.color_schemes["dark" if self.dark_mode else "light"]
            
            # تطبيق الألوان على النافذة الرئيسية
            self.root.configure(bg=colors["bg"])
            
            # تطبيق الألوان على الجدول
            style = ttk.Style()
            style.configure("Treeview", background=colors["table_bg"], foreground=colors["table_fg"], fieldbackground=colors["table_bg"])
            style.configure("Treeview.Heading", background=colors["highlight_bg"], foreground=colors["fg"])
            
            # تطبيق الألوان على العناصر الأخرى
            for widget in self.root.winfo_children():
                self.update_colors_recursive(widget, colors)
            
            print(f"[تم] تطبيق {'الوضع الداكن' if self.dark_mode else 'الوضع العادي'}")
            
        except Exception as e:
            print(f"[خطأ] فشل في تطبيق الوضع الداكن: {e}")
    
    def update_colors_recursive(self, widget, colors):
        """تحديث ألوان جميع العناصر بشكل متكرر"""
        try:
            # تحديث الألوان حسب نوع العنصر
            if isinstance(widget, tk.Frame):
                widget.configure(bg=colors["bg"])
            elif isinstance(widget, tk.Button):
                widget.configure(bg=colors["button_bg"], fg=colors["button_fg"], activebackground=colors["highlight_bg"])
            elif isinstance(widget, tk.Label):
                widget.configure(bg=colors["bg"], fg=colors["fg"])
            elif isinstance(widget, tk.Entry) or isinstance(widget, ttk.Combobox):
                # لا يمكن تغيير ألوان Combobox بسهولة
                if isinstance(widget, tk.Entry):
                    widget.configure(bg=colors["table_bg"], fg=colors["table_fg"], insertbackground=colors["fg"])
            
            # التكرار على العناصر الفرعية
            for child in widget.winfo_children():
                self.update_colors_recursive(child, colors)
                
        except Exception as e:
            # تجاهل الأخطاء للعناصر التي لا تدعم تغيير الألوان
            pass
            
    def toggle_dark_mode(self):
        """تبديل الوضع الداكن"""
        # تبديل حالة الوضع الداكن
        self.dark_mode = not self.dark_mode
        
        # تطبيق الوضع الداكن
        self.apply_dark_mode()
        
        # تحديث شريط الحالة
        mode_name = "الداكن" if self.dark_mode else "العادي"
        self.update_status(f"تم تغيير المظهر إلى الوضع {mode_name}")
        
        # حفظ الإعدادات
        if hasattr(self, 'config'):
            if "ui_settings" not in self.config:
                self.config["ui_settings"] = {}
            self.config["ui_settings"]["dark_mode"] = self.dark_mode
            try:
                with open(self.config_file, 'w', encoding='utf-8') as f:
                    json.dump(self.config, f, ensure_ascii=False, indent=4)
                print(f"[تم] حفظ إعدادات الوضع الداكن: {self.dark_mode}")
            except Exception as e:
                print(f"[خطأ] فشل في حفظ إعدادات الوضع الداكن: {e}")
    
    def load_font_settings(self):
        """تحميل إعدادات واجهة المستخدم من ملف الإعدادات"""
        if hasattr(self, 'config') and "ui_settings" in self.config:
            ui_settings = self.config.get("ui_settings", {})
            
            # تحميل حجم الخط
            saved_size = ui_settings.get("font_size")
            if saved_size in self.font_sizes:
                self.current_font_size = saved_size
                print(f"[تم] تحميل إعدادات حجم الخط: {saved_size}")
            
            # تحميل نوع الخط
            saved_font = ui_settings.get("font_family")
            if saved_font:
                self.current_font_family = saved_font
                print(f"[تم] تحميل إعدادات نوع الخط: {saved_font}")
            
            # تحميل إعدادات الوضع الداكن
            dark_mode = ui_settings.get("dark_mode")
            if dark_mode is not None:
                self.dark_mode = dark_mode
                print(f"[تم] تحميل إعدادات الوضع الداكن: {'مفعل' if dark_mode else 'غير مفعل'}")
                
                # تطبيق الوضع الداكن إذا كان مفعلاً
                if dark_mode:
                    self.apply_dark_mode()
            
            # تحميل إعدادات التلميحات
            show_tooltips = ui_settings.get("show_tooltips")
            if show_tooltips is not None:
                self.show_tooltips = show_tooltips
                print(f"[تم] تحميل إعدادات التلميحات: {'مفعلة' if show_tooltips else 'غير مفعلة'}")
    
    def close_window(self):
        """إغلاق النافذة الرئيسية"""
        if messagebox.askyesno("تأكيد الخروج", "هل أنت متأكد من الخروج من النظام؟"):
            # إيقاف خدمة النسخ الاحتياطي
            self.stop_backup_service()
            self.root.destroy()
            
    # ===== وظائف النسخ الاحتياطي =====
    
    def start_backup_service(self):
        """بدء خدمة النسخ الاحتياطي التلقائي"""
        if not self.config.get("backup", {}).get("enabled", False):
            print("[معلومات] خدمة النسخ الاحتياطي غير مفعلة")
            return
            
        if self.backup_running:
            print("[تحذير] خدمة النسخ الاحتياطي قيد التشغيل بالفعل")
            return
            
        self.backup_running = True
        self.backup_thread = threading.Thread(target=self.backup_scheduler, daemon=True)
        self.backup_thread.start()
        print("[تم] بدء خدمة النسخ الاحتياطي التلقائي")
        
    def stop_backup_service(self):
        """إيقاف خدمة النسخ الاحتياطي التلقائي"""
        if not self.backup_running:
            return
            
        self.backup_running = False
        if self.backup_thread:
            # انتظار انتهاء الخيط (بحد أقصى ثانية واحدة)
            if self.backup_thread.is_alive():
                self.backup_thread.join(1)
        print("[تم] إيقاف خدمة النسخ الاحتياطي التلقائي")
        
    def backup_scheduler(self):
        """جدولة النسخ الاحتياطي"""
        while self.backup_running:
            try:
                # التحقق مما إذا كان الوقت مناسباً لإجراء نسخة احتياطية
                if self.should_run_backup():
                    self.create_backup()
                    self.cleanup_old_backups()
                    self.update_last_backup_time()
                
                # انتظار دقيقة قبل التحقق مرة أخرى
                for _ in range(60):
                    if not self.backup_running:
                        break
                    time.sleep(1)
                    
            except Exception as e:
                print(f"[خطأ] خطأ في جدولة النسخ الاحتياطي: {e}")
                time.sleep(60)  # انتظار دقيقة قبل المحاولة مرة أخرى
                
    def should_run_backup(self):
        """التحقق مما إذا كان الوقت مناسباً لإجراء نسخة احتياطية"""
        backup_config = self.config.get("backup", {})
        schedule = backup_config.get("schedule", "daily")
        backup_time = backup_config.get("time", "00:00")
        last_backup = backup_config.get("last_backup")
        
        now = datetime.now()
        current_time = now.strftime("%H:%M")
        
        # إذا كان الوقت الحالي هو وقت النسخ الاحتياطي
        if current_time == backup_time:
            # إذا لم يتم إجراء نسخة احتياطية من قبل
            if not last_backup:
                return True
                
            # تحويل آخر وقت نسخ احتياطي إلى كائن datetime
            last_backup_time = datetime.strptime(last_backup, "%Y-%m-%d %H:%M:%S")
            
            # التحقق من الجدول
            if schedule == "daily":
                # التحقق مما إذا كان آخر نسخ احتياطي في يوم مختلف
                return last_backup_time.date() < now.date()
            elif schedule == "weekly":
                # التحقق مما إذا كان آخر نسخ احتياطي قبل أسبوع على الأقل
                return (now - last_backup_time).days >= 7
            elif schedule == "monthly":
                # التحقق مما إذا كان آخر نسخ احتياطي في شهر مختلف
                return (last_backup_time.year != now.year or last_backup_time.month != now.month)
        
        return False
        
    def create_backup(self):
        """إنشاء نسخة احتياطية من ملف البيانات"""
        try:
            if not os.path.exists(self.excel_file):
                print("[تحذير] ملف البيانات غير موجود، لا يمكن إنشاء نسخة احتياطية")
                return False
                
            backup_dir = self.config.get("backup", {}).get("backup_dir", "backups")
            if not os.path.exists(backup_dir):
                os.makedirs(backup_dir)
                
            # إنشاء اسم ملف النسخة الاحتياطية
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"employees_data_backup_{timestamp}.xlsx"
            backup_path = os.path.join(backup_dir, backup_filename)
            
            # نسخ الملف
            shutil.copy2(self.excel_file, backup_path)
            
            print(f"[تم] إنشاء نسخة احتياطية: {backup_path}")
            return True
            
        except Exception as e:
            print(f"[خطأ] فشل في إنشاء نسخة احتياطية: {e}")
            return False
            
    def cleanup_old_backups(self):
        """حذف النسخ الاحتياطية القديمة"""
        try:
            backup_dir = self.config.get("backup", {}).get("backup_dir", "backups")
            keep_days = self.config.get("backup", {}).get("keep_days", 7)
            
            if not os.path.exists(backup_dir):
                return
                
            # حساب التاريخ الذي يجب حذف النسخ الاحتياطية قبله
            cutoff_date = datetime.now() - timedelta(days=keep_days)
            
            # الحصول على قائمة ملفات النسخ الاحتياطية
            backup_files = glob.glob(os.path.join(backup_dir, "employees_data_backup_*.xlsx"))
            
            for backup_file in backup_files:
                try:
                    # استخراج التاريخ من اسم الملف
                    filename = os.path.basename(backup_file)
                    date_str = filename.replace("employees_data_backup_", "").replace(".xlsx", "")
                    file_date = datetime.strptime(date_str, "%Y%m%d_%H%M%S")
                    
                    # إذا كان الملف أقدم من تاريخ القطع، قم بحذفه
                    if file_date < cutoff_date:
                        os.remove(backup_file)
                        print(f"[تم] حذف نسخة احتياطية قديمة: {filename}")
                except Exception as e:
                    print(f"[تحذير] فشل في معالجة ملف النسخ الاحتياطي {filename}: {e}")
                    
        except Exception as e:
            print(f"[خطأ] فشل في تنظيف النسخ الاحتياطية القديمة: {e}")
            
    def update_last_backup_time(self):
        """تحديث وقت آخر نسخة احتياطية في ملف الإعدادات"""
        try:
            # تحديث وقت آخر نسخة احتياطية
            self.config["backup"]["last_backup"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            # حفظ الإعدادات
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=4)
                
        except Exception as e:
            print(f"[خطأ] فشل في تحديث وقت آخر نسخة احتياطية: {e}")
            
    def manual_backup(self):
        """إنشاء نسخة احتياطية يدوياً"""
        if self.create_backup():
            self.update_last_backup_time()
            messagebox.showinfo("تم بنجاح", "تم إنشاء نسخة احتياطية بنجاح")
        else:
            messagebox.showerror("خطأ", "فشل في إنشاء نسخة احتياطية")

    # ===== وظائف الرقم الوظيفي =====

    def show_last_employee_id(self):
        """عرض آخر رقم وظيفي في رسالة"""
        try:
            # استخدام الوظيفة الصحيحة التي تأخذ form_vars
            form_vars = {"الرقم الوظيفي": tk.StringVar()}
            self.get_last_employee_id(form_vars)

            last_id_str = form_vars["الرقم الوظيفي"].get()
            if last_id_str:
                try:
                    suggested_next = int(last_id_str)
                    last_id = suggested_next - 1

                    if last_id == 0:
                        message = "لا توجد أرقام وظيفية في النظام.\nيمكنك البدء بالرقم 1."
                    else:
                        message = f"آخر رقم وظيفي في النظام: {last_id}\nالرقم المقترح للموظف الجديد: {suggested_next}"

                    # عرض الرسالة مع خيار نسخ الرقم المقترح
                    result = messagebox.askquestion(
                        "آخر رقم وظيفي",
                        f"{message}\n\nهل تريد نسخ الرقم المقترح ({suggested_next}) إلى الحافظة؟",
                        icon='question'
                    )

                    if result == 'yes':
                        self.root.clipboard_clear()
                        self.root.clipboard_append(str(suggested_next))
                        self.update_status(f"تم نسخ الرقم المقترح: {suggested_next}")

                except ValueError:
                    messagebox.showerror("خطأ", "خطأ في قراءة الرقم الوظيفي")
            else:
                messagebox.showinfo("معلومات", "لا توجد أرقام وظيفية في النظام")

        except Exception as e:
            print(f"[خطأ] خطأ في عرض آخر رقم وظيفي: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ غير متوقع:\n{str(e)}")

    # ===== وظائف قالب الإحصائيات =====

    def load_statistics_template(self):
        """تحميل قالب الإحصائيات"""
        template_file = "statistics_template.txt"
        try:
            if os.path.exists(template_file):
                with open(template_file, 'r', encoding='utf-8') as f:
                    return f.read()
            else:
                # إنشاء قالب افتراضي إذا لم يكن موجوداً
                return self.get_default_template()
        except Exception as e:
            print(f"[خطأ] فشل في تحميل قالب الإحصائيات: {e}")
            return self.get_default_template()

    def save_statistics_template(self, template_content):
        """حفظ قالب الإحصائيات"""
        template_file = "statistics_template.txt"
        try:
            with open(template_file, 'w', encoding='utf-8') as f:
                f.write(template_content)
            return True
        except Exception as e:
            print(f"[خطأ] فشل في حفظ قالب الإحصائيات: {e}")
            return False

    def get_default_template(self):
        """الحصول على القالب الافتراضي المحسن لـ Word"""
        return """╔══════════════════════════════════════════════════════════════╗
║                        {TITLE}                              ║
╚══════════════════════════════════════════════════════════════╝

📅 تاريخ التقرير: {DATE}
🕐 وقت الإنشاء: {TIME}

═══════════════════════════════════════════════════════════════

📊 الإحصائيات العامة
═══════════════════════════════════════════════════════════════

👥 إجمالي عدد الموظفين: {TOTAL_EMPLOYEES} موظف

═══════════════════════════════════════════════════════════════

👨‍👩‍👧‍👦 توزيع الموظفين حسب الجنس
═══════════════════════════════════════════════════════════════

👨 الذكور: {MALES} موظف ({MALE_PERCENT}%)
👩 الإناث: {FEMALES} موظف ({FEMALE_PERCENT}%)

═══════════════════════════════════════════════════════════════

🏢 توزيع الموظفين حسب أماكن العمل
═══════════════════════════════════════════════════════════════

{WORKPLACE_DATA}

═══════════════════════════════════════════════════════════════

📋 معلومات إضافية
═══════════════════════════════════════════════════════════════

✅ تم إنشاء هذا التقرير بواسطة نظام إدارة الموظفين المبسط
✅ البيانات محدثة حتى تاريخ إنشاء التقرير
✅ توزيع الجنس يعتمد على الرقم الوطني (الرقم قبل الأخير: 1=ذكر، 2=أنثى)
✅ جميع الإحصائيات مبنية على البيانات المتاحة في النظام

═══════════════════════════════════════════════════════════════

🔚 نهاية التقرير - شكراً لاستخدام النظام

═══════════════════════════════════════════════════════════════"""

    def show_template_location_and_editor(self):
        """عرض مكان ملف القالب والسماح بالتعديل عليه"""
        try:
            # إنشاء نافذة عرض مكان القالب
            location_window = tk.Toplevel(self.root)
            location_window.title("مكان ملف قالب التصدير")
            location_window.geometry("800x600")
            location_window.configure(bg="#f0f0f0")

            # توسيط النافذة
            self.center_window(location_window, 800, 600)

            # إنشاء إطار رئيسي
            main_frame = tk.Frame(location_window, bg="#f0f0f0")
            main_frame.pack(fill="both", expand=True, padx=20, pady=20)

            # العنوان
            title_label = tk.Label(main_frame, text="📁 مكان ملف قالب التصدير",
                                 font=("Arial", 16, "bold"), bg="#f0f0f0", fg="#2c3e50")
            title_label.pack(pady=(0, 20))

            # إطار معلومات الملف
            file_info_frame = tk.LabelFrame(main_frame, text="معلومات ملف القالب",
                                          bg="#e8f4fd", font=("Arial", 12, "bold"))
            file_info_frame.pack(fill="x", pady=(0, 20))

            # مسار الملف
            template_file = "statistics_template.txt"
            full_path = os.path.abspath(template_file)

            # معلومات الملف
            file_exists = os.path.exists(template_file)
            file_size = os.path.getsize(template_file) if file_exists else 0

            # عرض معلومات الملف
            info_text = f"""📄 اسم الملف: {template_file}
📂 المسار الكامل: {full_path}
📊 حالة الملف: {'موجود' if file_exists else 'غير موجود'}
📏 حجم الملف: {file_size} بايت
🕐 آخر تعديل: {datetime.fromtimestamp(os.path.getmtime(template_file)).strftime('%Y/%m/%d %H:%M:%S') if file_exists else 'غير متاح'}"""

            info_label = tk.Label(file_info_frame, text=info_text,
                                justify="right", bg="#e8f4fd", font=("Arial", 10))
            info_label.pack(padx=10, pady=10)

            # إطار الأزرار العلوية
            top_buttons_frame = tk.Frame(main_frame, bg="#f0f0f0")
            top_buttons_frame.pack(fill="x", pady=(0, 20))

            # زر فتح مجلد الملف
            def open_file_location():
                try:
                    if os.path.exists(template_file):
                        # فتح مجلد الملف وتحديد الملف
                        os.system(f'explorer /select,"{full_path}"')
                        self.update_status("تم فتح مجلد الملف")
                    else:
                        messagebox.showwarning("تنبيه", "ملف القالب غير موجود")
                except Exception as e:
                    messagebox.showerror("خطأ", f"فشل في فتح مجلد الملف:\n{str(e)}")

            open_folder_button = tk.Button(top_buttons_frame, text="📂 فتح مجلد الملف",
                                         command=open_file_location,
                                         bg="#27ae60", fg="white", font=("Arial", 12, "bold"),
                                         padx=20, pady=10)
            open_folder_button.pack(side=tk.LEFT, padx=10)

            # زر فتح الملف في المحرر الافتراضي
            def open_in_default_editor():
                try:
                    if os.path.exists(template_file):
                        os.startfile(template_file)
                        self.update_status("تم فتح الملف في المحرر الافتراضي")
                    else:
                        messagebox.showwarning("تنبيه", "ملف القالب غير موجود")
                except Exception as e:
                    messagebox.showerror("خطأ", f"فشل في فتح الملف:\n{str(e)}")

            open_editor_button = tk.Button(top_buttons_frame, text="📝 فتح في المحرر",
                                         command=open_in_default_editor,
                                         bg="#e67e22", fg="white", font=("Arial", 12, "bold"),
                                         padx=20, pady=10)
            open_editor_button.pack(side=tk.LEFT, padx=10)

            # زر إنشاء ملف جديد إذا لم يكن موجوداً
            def create_template_file():
                try:
                    if not os.path.exists(template_file):
                        default_template = self.get_default_template()
                        with open(template_file, 'w', encoding='utf-8') as f:
                            f.write(default_template)
                        messagebox.showinfo("تم الإنشاء", f"تم إنشاء ملف القالب:\n{full_path}")
                        location_window.destroy()
                        self.show_template_location_and_editor()  # إعادة فتح النافذة
                    else:
                        messagebox.showinfo("معلومات", "ملف القالب موجود بالفعل")
                except Exception as e:
                    messagebox.showerror("خطأ", f"فشل في إنشاء الملف:\n{str(e)}")

            if not file_exists:
                create_button = tk.Button(top_buttons_frame, text="➕ إنشاء ملف القالب",
                                        command=create_template_file,
                                        bg="#9b59b6", fg="white", font=("Arial", 12, "bold"),
                                        padx=20, pady=10)
                create_button.pack(side=tk.LEFT, padx=10)

            # زر فتح المحرر المدمج
            def open_integrated_editor():
                location_window.destroy()
                self.show_template_editor()

            integrated_editor_button = tk.Button(top_buttons_frame, text="🔧 المحرر المدمج",
                                                command=open_integrated_editor,
                                                bg="#3498db", fg="white", font=("Arial", 12, "bold"),
                                                padx=20, pady=10)
            integrated_editor_button.pack(side=tk.LEFT, padx=10)

            # إطار معاينة محتوى الملف
            preview_frame = tk.LabelFrame(main_frame, text="معاينة محتوى القالب",
                                        bg="#f9f9f9", font=("Arial", 12, "bold"))
            preview_frame.pack(fill="both", expand=True, pady=(0, 20))

            # إنشاء منطقة نص للمعاينة
            preview_text_frame = tk.Frame(preview_frame, bg="#f9f9f9")
            preview_text_frame.pack(fill="both", expand=True, padx=10, pady=10)

            # منطقة النص مع أشرطة التمرير
            preview_text = tk.Text(preview_text_frame, wrap=tk.WORD, font=("Courier New", 10),
                                 bg="white", fg="black", state=tk.DISABLED)

            # أشرطة التمرير للمعاينة
            preview_scrollbar_v = tk.Scrollbar(preview_text_frame, orient=tk.VERTICAL, command=preview_text.yview)
            preview_scrollbar_h = tk.Scrollbar(preview_text_frame, orient=tk.HORIZONTAL, command=preview_text.xview)

            preview_text.config(yscrollcommand=preview_scrollbar_v.set, xscrollcommand=preview_scrollbar_h.set)

            # ترتيب العناصر
            preview_text.pack(side=tk.LEFT, fill="both", expand=True)
            preview_scrollbar_v.pack(side=tk.RIGHT, fill=tk.Y)
            preview_scrollbar_h.pack(side=tk.BOTTOM, fill=tk.X)

            # تحميل وعرض محتوى الملف
            def load_and_display_content():
                try:
                    preview_text.config(state=tk.NORMAL)
                    preview_text.delete("1.0", tk.END)

                    if os.path.exists(template_file):
                        with open(template_file, 'r', encoding='utf-8') as f:
                            content = f.read()
                        preview_text.insert("1.0", content)
                        preview_text.insert("1.0", "📄 محتوى ملف القالب:\n" + "="*50 + "\n\n")
                    else:
                        default_content = self.get_default_template()
                        preview_text.insert("1.0", default_content)
                        preview_text.insert("1.0", "📄 القالب الافتراضي (الملف غير موجود):\n" + "="*50 + "\n\n")

                    preview_text.config(state=tk.DISABLED)
                except Exception as e:
                    preview_text.config(state=tk.NORMAL)
                    preview_text.delete("1.0", tk.END)
                    preview_text.insert("1.0", f"❌ خطأ في قراءة الملف:\n{str(e)}")
                    preview_text.config(state=tk.DISABLED)

            # تحميل المحتوى عند فتح النافذة
            load_and_display_content()

            # إطار الأزرار السفلية
            bottom_buttons_frame = tk.Frame(main_frame, bg="#f0f0f0")
            bottom_buttons_frame.pack(fill="x")

            # زر تحديث المعاينة
            def refresh_preview():
                load_and_display_content()
                self.update_status("تم تحديث معاينة القالب")

            refresh_button = tk.Button(bottom_buttons_frame, text="🔄 تحديث المعاينة",
                                     command=refresh_preview,
                                     bg="#17a2b8", fg="white", font=("Arial", 12, "bold"),
                                     padx=20, pady=10)
            refresh_button.pack(side=tk.LEFT, padx=10)

            # زر نسخ المسار
            def copy_path():
                try:
                    location_window.clipboard_clear()
                    location_window.clipboard_append(full_path)
                    messagebox.showinfo("تم النسخ", f"تم نسخ مسار الملف إلى الحافظة:\n{full_path}")
                    self.update_status("تم نسخ مسار الملف")
                except Exception as e:
                    messagebox.showerror("خطأ", f"فشل في نسخ المسار:\n{str(e)}")

            copy_path_button = tk.Button(bottom_buttons_frame, text="📋 نسخ المسار",
                                       command=copy_path,
                                       bg="#6c757d", fg="white", font=("Arial", 12, "bold"),
                                       padx=20, pady=10)
            copy_path_button.pack(side=tk.LEFT, padx=10)

            # زر الإغلاق
            close_button = tk.Button(bottom_buttons_frame, text="إغلاق",
                                   command=location_window.destroy,
                                   bg="#dc3545", fg="white", font=("Arial", 12, "bold"),
                                   padx=20, pady=10)
            close_button.pack(side=tk.RIGHT, padx=10)

        except Exception as e:
            print(f"[خطأ] خطأ في عرض مكان القالب: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ أثناء عرض مكان القالب:\n{str(e)}")

    def show_template_editor(self):
        """عرض محرر قالب الإحصائيات"""
        try:
            # إنشاء نافذة المحرر
            editor_window = tk.Toplevel(self.root)
            editor_window.title("محرر قالب تصدير الإحصائيات")
            editor_window.geometry("900x700")
            editor_window.configure(bg="#f0f0f0")

            # توسيط النافذة
            self.center_window(editor_window, 900, 700)

            # إنشاء إطار رئيسي
            main_frame = tk.Frame(editor_window, bg="#f0f0f0")
            main_frame.pack(fill="both", expand=True, padx=20, pady=20)

            # العنوان
            title_label = tk.Label(main_frame, text="📝 محرر قالب تصدير الإحصائيات",
                                 font=("Arial", 16, "bold"), bg="#f0f0f0", fg="#2c3e50")
            title_label.pack(pady=(0, 20))

            # إطار التعليمات
            instructions_frame = tk.LabelFrame(main_frame, text="التعليمات",
                                             bg="#e8f4fd", font=("Arial", 12, "bold"))
            instructions_frame.pack(fill="x", pady=(0, 20))

            instructions_text = """يمكنك تعديل قالب تصدير الإحصائيات باستخدام المتغيرات التالية:
• {TITLE} - عنوان التقرير
• {DATE} - تاريخ التقرير
• {TIME} - وقت التقرير
• {TOTAL_EMPLOYEES} - إجمالي عدد الموظفين
• {MALES} - عدد الذكور
• {FEMALES} - عدد الإناث
• {MALE_PERCENT} - نسبة الذكور
• {FEMALE_PERCENT} - نسبة الإناث
• {WORKPLACE_DATA} - بيانات أماكن العمل"""

            instructions_label = tk.Label(instructions_frame, text=instructions_text,
                                        justify="right", bg="#e8f4fd", font=("Arial", 10))
            instructions_label.pack(padx=10, pady=10)

            # إطار المحرر
            editor_frame = tk.LabelFrame(main_frame, text="محتوى القالب",
                                       bg="#ffffff", font=("Arial", 12, "bold"))
            editor_frame.pack(fill="both", expand=True, pady=(0, 20))

            # منطقة النص مع شريط التمرير
            text_frame = tk.Frame(editor_frame, bg="#ffffff")
            text_frame.pack(fill="both", expand=True, padx=10, pady=10)

            # شريط التمرير العمودي
            scrollbar_v = tk.Scrollbar(text_frame)
            scrollbar_v.pack(side=tk.RIGHT, fill=tk.Y)

            # شريط التمرير الأفقي
            scrollbar_h = tk.Scrollbar(text_frame, orient=tk.HORIZONTAL)
            scrollbar_h.pack(side=tk.BOTTOM, fill=tk.X)

            # منطقة النص
            text_widget = tk.Text(text_frame, wrap=tk.NONE, font=("Courier New", 11),
                                yscrollcommand=scrollbar_v.set,
                                xscrollcommand=scrollbar_h.set,
                                bg="#ffffff", fg="#2c3e50", insertbackground="#2c3e50")
            text_widget.pack(fill="both", expand=True)

            # ربط أشرطة التمرير
            scrollbar_v.config(command=text_widget.yview)
            scrollbar_h.config(command=text_widget.xview)

            # تحميل القالب الحالي
            current_template = self.load_statistics_template()
            text_widget.insert("1.0", current_template)

            # إطار الأزرار
            buttons_frame = tk.Frame(main_frame, bg="#f0f0f0")
            buttons_frame.pack(fill="x")

            # وظيفة الحفظ
            def save_template():
                try:
                    template_content = text_widget.get("1.0", tk.END).strip()
                    if self.save_statistics_template(template_content):
                        messagebox.showinfo("تم الحفظ", "تم حفظ القالب بنجاح!")
                        self.update_status("تم حفظ قالب الإحصائيات")
                    else:
                        messagebox.showerror("خطأ", "فشل في حفظ القالب")
                except Exception as e:
                    messagebox.showerror("خطأ", f"حدث خطأ أثناء الحفظ:\n{str(e)}")

            # وظيفة الاستعادة
            def reset_template():
                if messagebox.askyesno("تأكيد الاستعادة",
                                     "هل أنت متأكد من استعادة القالب الافتراضي؟\nسيتم فقدان التعديلات الحالية."):
                    default_template = self.get_default_template()
                    text_widget.delete("1.0", tk.END)
                    text_widget.insert("1.0", default_template)

            # وظيفة المعاينة
            def preview_template():
                try:
                    template_content = text_widget.get("1.0", tk.END).strip()
                    # إنشاء بيانات تجريبية للمعاينة
                    preview_data = {
                        'TITLE': 'إحصائيات الموظفين - معاينة',
                        'DATE': datetime.now().strftime("%Y/%m/%d"),
                        'TIME': datetime.now().strftime("%H:%M:%S"),
                        'TOTAL_EMPLOYEES': '150',
                        'MALES': '90',
                        'FEMALES': '60',
                        'MALE_PERCENT': '60.0',
                        'FEMALE_PERCENT': '40.0',
                        'WORKPLACE_DATA': 'المكتب الرئيسي: 100 موظف (66.7%)\nالفرع الأول: 30 موظف (20.0%)\nالفرع الثاني: 20 موظف (13.3%)'
                    }

                    # تطبيق البيانات على القالب
                    preview_content = template_content
                    for key, value in preview_data.items():
                        preview_content = preview_content.replace(f'{{{key}}}', value)

                    # عرض المعاينة
                    self.show_template_preview(preview_content)

                except Exception as e:
                    messagebox.showerror("خطأ", f"حدث خطأ أثناء المعاينة:\n{str(e)}")

            # الأزرار
            save_button = tk.Button(buttons_frame, text="💾 حفظ القالب",
                                  command=save_template,
                                  bg="#27ae60", fg="white", font=("Arial", 12, "bold"),
                                  padx=20, pady=10)
            save_button.pack(side=tk.LEFT, padx=(0, 10))

            preview_button = tk.Button(buttons_frame, text="👁️ معاينة",
                                     command=preview_template,
                                     bg="#3498db", fg="white", font=("Arial", 12, "bold"),
                                     padx=20, pady=10)
            preview_button.pack(side=tk.LEFT, padx=(0, 10))

            reset_button = tk.Button(buttons_frame, text="🔄 استعادة الافتراضي",
                                   command=reset_template,
                                   bg="#e74c3c", fg="white", font=("Arial", 12, "bold"),
                                   padx=20, pady=10)
            reset_button.pack(side=tk.LEFT, padx=(0, 10))

            close_button = tk.Button(buttons_frame, text="❌ إغلاق",
                                   command=editor_window.destroy,
                                   bg="#95a5a6", fg="white", font=("Arial", 12, "bold"),
                                   padx=20, pady=10)
            close_button.pack(side=tk.RIGHT)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في فتح محرر القالب:\n{str(e)}")
            print(f"[خطأ] خطأ في محرر القالب: {e}")

    def show_template_preview(self, preview_content):
        """عرض معاينة القالب"""
        try:
            # إنشاء نافذة المعاينة
            preview_window = tk.Toplevel(self.root)
            preview_window.title("معاينة قالب الإحصائيات")
            preview_window.geometry("800x600")
            preview_window.configure(bg="#f0f0f0")

            # توسيط النافذة
            self.center_window(preview_window, 800, 600)

            # إنشاء إطار رئيسي
            main_frame = tk.Frame(preview_window, bg="#f0f0f0")
            main_frame.pack(fill="both", expand=True, padx=20, pady=20)

            # العنوان
            title_label = tk.Label(main_frame, text="👁️ معاينة قالب الإحصائيات",
                                 font=("Arial", 16, "bold"), bg="#f0f0f0", fg="#2c3e50")
            title_label.pack(pady=(0, 20))

            # إطار المعاينة
            preview_frame = tk.LabelFrame(main_frame, text="المعاينة (بيانات تجريبية)",
                                        bg="#ffffff", font=("Arial", 12, "bold"))
            preview_frame.pack(fill="both", expand=True, pady=(0, 20))

            # منطقة النص مع شريط التمرير
            text_frame = tk.Frame(preview_frame, bg="#ffffff")
            text_frame.pack(fill="both", expand=True, padx=10, pady=10)

            # شريط التمرير
            scrollbar = tk.Scrollbar(text_frame)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            # منطقة النص
            text_widget = tk.Text(text_frame, wrap=tk.WORD, font=("Courier New", 11),
                                yscrollcommand=scrollbar.set, state=tk.DISABLED,
                                bg="#f8f9fa", fg="#2c3e50")
            text_widget.pack(fill="both", expand=True)

            # ربط شريط التمرير
            scrollbar.config(command=text_widget.yview)

            # إدراج المحتوى
            text_widget.config(state=tk.NORMAL)
            text_widget.insert("1.0", preview_content)
            text_widget.config(state=tk.DISABLED)

            # إطار الأزرار
            buttons_frame = tk.Frame(main_frame, bg="#f0f0f0")
            buttons_frame.pack(fill="x")

            # زر الإغلاق
            close_button = tk.Button(buttons_frame, text="❌ إغلاق",
                                   command=preview_window.destroy,
                                   bg="#95a5a6", fg="white", font=("Arial", 12, "bold"),
                                   padx=20, pady=10)
            close_button.pack(side=tk.RIGHT)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في عرض المعاينة:\n{str(e)}")
            print(f"[خطأ] خطأ في معاينة القالب: {e}")
            
    def show_backup_settings(self):
        """عرض إعدادات النسخ الاحتياطي"""
        # إنشاء نافذة الإعدادات
        settings_window = tk.Toplevel(self.root)
        settings_window.title("إعدادات النسخ الاحتياطي")
        settings_window.geometry("500x500")
        settings_window.configure(bg="#f0f0f0")
        
        # إنشاء إطار الإعدادات
        settings_frame = tk.Frame(settings_window, bg="#ffffff", padx=20, pady=20)
        settings_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # العنوان
        title_label = tk.Label(settings_frame, text="إعدادات النسخ الاحتياطي", font=("Arial", 16, "bold"), bg="#ffffff")
        title_label.pack(pady=(0, 20))
        
        # تفعيل النسخ الاحتياطي
        enabled_var = tk.BooleanVar(value=self.config.get("backup", {}).get("enabled", False))
        enabled_check = tk.Checkbutton(settings_frame, text="تفعيل النسخ الاحتياطي التلقائي", variable=enabled_var, bg="#ffffff", font=("Arial", 12))
        enabled_check.pack(anchor="w", pady=5)
        
        # إطار الجدول
        schedule_frame = tk.LabelFrame(settings_frame, text="جدول النسخ الاحتياطي", bg="#ffffff", font=("Arial", 12))
        schedule_frame.pack(fill="x", pady=10)
        
        # خيارات الجدول
        schedule_var = tk.StringVar(value=self.config.get("backup", {}).get("schedule", "daily"))
        schedule_options = [
            ("يومي", "daily"),
            ("أسبوعي", "weekly"),
            ("شهري", "monthly")
        ]
        
        for i, (text, value) in enumerate(schedule_options):
            rb = tk.Radiobutton(schedule_frame, text=text, variable=schedule_var, value=value, bg="#ffffff", font=("Arial", 12))
            rb.pack(anchor="w", padx=20, pady=5)
        
        # وقت النسخ الاحتياطي
        time_frame = tk.Frame(settings_frame, bg="#ffffff")
        time_frame.pack(fill="x", pady=10)
        
        time_label = tk.Label(time_frame, text="وقت النسخ الاحتياطي:", bg="#ffffff", font=("Arial", 12))
        time_label.pack(side="left", padx=(0, 10))
        
        current_time = self.config.get("backup", {}).get("time", "00:00")
        hour, minute = current_time.split(":")
        
        hour_var = tk.StringVar(value=hour)
        hour_options = [f"{i:02d}" for i in range(24)]
        hour_menu = ttk.Combobox(time_frame, textvariable=hour_var, values=hour_options, width=5)
        hour_menu.pack(side="left")
        
        separator_label = tk.Label(time_frame, text=":", bg="#ffffff", font=("Arial", 12))
        separator_label.pack(side="left", padx=2)
        
        minute_var = tk.StringVar(value=minute)
        minute_options = [f"{i:02d}" for i in range(0, 60, 5)]
        minute_menu = ttk.Combobox(time_frame, textvariable=minute_var, values=minute_options, width=5)
        minute_menu.pack(side="left")
        
        # عدد الأيام للاحتفاظ بالنسخ الاحتياطية
        keep_frame = tk.Frame(settings_frame, bg="#ffffff")
        keep_frame.pack(fill="x", pady=10)
        
        keep_label = tk.Label(keep_frame, text="الاحتفاظ بالنسخ الاحتياطية لمدة:", bg="#ffffff", font=("Arial", 12))
        keep_label.pack(side="left", padx=(0, 10))
        
        keep_days_var = tk.StringVar(value=str(self.config.get("backup", {}).get("keep_days", 7)))
        keep_days_entry = tk.Entry(keep_frame, textvariable=keep_days_var, width=5)
        keep_days_entry.pack(side="left")
        
        days_label = tk.Label(keep_frame, text="أيام", bg="#ffffff", font=("Arial", 12))
        days_label.pack(side="left", padx=(5, 0))
        
        # مجلد النسخ الاحتياطي
        dir_frame = tk.Frame(settings_frame, bg="#ffffff")
        dir_frame.pack(fill="x", pady=10)
        
        dir_label = tk.Label(dir_frame, text="مجلد النسخ الاحتياطي:", bg="#ffffff", font=("Arial", 12))
        dir_label.pack(side="left", padx=(0, 10))
        
        backup_dir_var = tk.StringVar(value=self.config.get("backup", {}).get("backup_dir", "backups"))
        dir_entry = tk.Entry(dir_frame, textvariable=backup_dir_var, width=20)
        dir_entry.pack(side="left", fill="x", expand=True)
        
        # معلومات آخر نسخة احتياطية
        last_backup = self.config.get("backup", {}).get("last_backup")
        if last_backup:
            last_backup_frame = tk.Frame(settings_frame, bg="#ffffff")
            last_backup_frame.pack(fill="x", pady=10)
            
            last_backup_label = tk.Label(last_backup_frame, text=f"آخر نسخة احتياطية: {last_backup}", bg="#ffffff", font=("Arial", 12))
            last_backup_label.pack(anchor="w")
        
        # إطار الأزرار
        buttons_frame = tk.Frame(settings_window, bg="#f0f0f0")
        buttons_frame.pack(pady=20)
        
        # وظيفة حفظ الإعدادات
        def save_settings():
            try:
                # تحديث الإعدادات
                if "backup" not in self.config:
                    self.config["backup"] = {}
                
                self.config["backup"]["enabled"] = enabled_var.get()
                self.config["backup"]["schedule"] = schedule_var.get()
                self.config["backup"]["time"] = f"{hour_var.get()}:{minute_var.get()}"
                
                try:
                    keep_days = int(keep_days_var.get())
                    if keep_days < 1:
                        keep_days = 1
                    self.config["backup"]["keep_days"] = keep_days
                except ValueError:
                    messagebox.showerror("خطأ", "يجب أن يكون عدد الأيام رقماً صحيحاً")
                    return
                
                backup_dir = backup_dir_var.get().strip()
                if not backup_dir:
                    backup_dir = "backups"
                self.config["backup"]["backup_dir"] = backup_dir
                
                # حفظ الإعدادات
                with open(self.config_file, 'w', encoding='utf-8') as f:
                    json.dump(self.config, f, ensure_ascii=False, indent=4)
                
                # إنشاء مجلد النسخ الاحتياطي إذا لم يكن موجوداً
                if enabled_var.get() and not os.path.exists(backup_dir):
                    os.makedirs(backup_dir)
                    print(f"[تم] إنشاء مجلد النسخ الاحتياطي: {backup_dir}")
                
                # إعادة تشغيل خدمة النسخ الاحتياطي
                self.stop_backup_service()
                self.start_backup_service()
                
                messagebox.showinfo("تم بنجاح", "تم حفظ إعدادات النسخ الاحتياطي بنجاح")
                settings_window.destroy()
                
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ الإعدادات:\n{str(e)}")
        
        # زر الحفظ
        save_button = tk.Button(buttons_frame, text="حفظ الإعدادات", 
                              command=save_settings,
                              bg="#27ae60", fg="white", font=("Arial", 12, "bold"),
                              padx=20, pady=10)
        save_button.pack(side=tk.LEFT, padx=10)
        
        # زر الإلغاء
        cancel_button = tk.Button(buttons_frame, text="إلغاء", 
                               command=settings_window.destroy,
                               bg="#e74c3c", fg="white", font=("Arial", 12, "bold"),
                               padx=20, pady=10)
        cancel_button.pack(side=tk.LEFT, padx=10)
        
        # زر إنشاء نسخة احتياطية الآن
        backup_now_button = tk.Button(settings_frame, text="إنشاء نسخة احتياطية الآن", 
                                   command=lambda: [self.manual_backup(), settings_window.lift()],
                                   bg="#3498db", fg="white", font=("Arial", 12, "bold"),
                                   padx=20, pady=10)
        backup_now_button.pack(pady=20)
        
        # زر عرض النسخ الاحتياطية
        show_backups_button = tk.Button(settings_frame, text="عرض النسخ الاحتياطية", 
                                     command=self.show_backup_files,
                                     bg="#9b59b6", fg="white", font=("Arial", 12, "bold"),
                                     padx=20, pady=10)
        show_backups_button.pack(pady=5)
    
    def show_backup_files(self):
        """عرض ملفات النسخ الاحتياطية"""
        backup_dir = self.config.get("backup", {}).get("backup_dir", "backups")
        
        if not os.path.exists(backup_dir):
            messagebox.showinfo("معلومات", "مجلد النسخ الاحتياطي غير موجود")
            return
        
        # إنشاء نافذة عرض النسخ الاحتياطية
        backups_window = tk.Toplevel(self.root)
        backups_window.title("النسخ الاحتياطية")
        backups_window.geometry("700x500")
        backups_window.configure(bg="#f0f0f0")
        
        # إنشاء إطار العرض
        backups_frame = tk.Frame(backups_window, bg="#ffffff", padx=20, pady=20)
        backups_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # العنوان
        title_label = tk.Label(backups_frame, text="النسخ الاحتياطية المتوفرة", font=("Arial", 16, "bold"), bg="#ffffff")
        title_label.pack(pady=(0, 20))
        
        # إنشاء جدول النسخ الاحتياطية
        columns = ("filename", "date", "size")
        backups_tree = ttk.Treeview(backups_frame, columns=columns, show="headings")
        
        # تعيين عناوين الأعمدة
        backups_tree.heading("filename", text="اسم الملف")
        backups_tree.heading("date", text="تاريخ النسخة")
        backups_tree.heading("size", text="حجم الملف")
        
        # تعيين عرض الأعمدة
        backups_tree.column("filename", width=300)
        backups_tree.column("date", width=150)
        backups_tree.column("size", width=100)
        
        # إضافة شريط التمرير
        scrollbar = ttk.Scrollbar(backups_frame, orient="vertical", command=backups_tree.yview)
        backups_tree.configure(yscrollcommand=scrollbar.set)
        
        # وضع الجدول وشريط التمرير
        backups_tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # الحصول على قائمة ملفات النسخ الاحتياطية
        backup_files = glob.glob(os.path.join(backup_dir, "employees_data_backup_*.xlsx"))
        
        # إضافة البيانات إلى الجدول
        for backup_file in sorted(backup_files, reverse=True):
            try:
                filename = os.path.basename(backup_file)
                
                # استخراج التاريخ من اسم الملف
                date_str = filename.replace("employees_data_backup_", "").replace(".xlsx", "")
                file_date = datetime.strptime(date_str, "%Y%m%d_%H%M%S")
                formatted_date = file_date.strftime("%Y/%m/%d %H:%M:%S")
                
                # حساب حجم الملف
                file_size = os.path.getsize(backup_file)
                if file_size < 1024:
                    size_str = f"{file_size} بايت"
                elif file_size < 1024 * 1024:
                    size_str = f"{file_size / 1024:.1f} كيلوبايت"
                else:
                    size_str = f"{file_size / (1024 * 1024):.1f} ميجابايت"
                
                backups_tree.insert("", "end", values=(filename, formatted_date, size_str))
                
            except Exception as e:
                print(f"[تحذير] فشل في معالجة ملف النسخ الاحتياطي {filename}: {e}")
        
        # إطار الأزرار
        buttons_frame = tk.Frame(backups_window, bg="#f0f0f0")
        buttons_frame.pack(pady=20)
        
        # وظيفة استعادة النسخة الاحتياطية
        def restore_backup():
            selected_items = backups_tree.selection()
            if not selected_items:
                messagebox.showinfo("تنبيه", "الرجاء تحديد نسخة احتياطية للاستعادة")
                return
            
            selected_item = selected_items[0]
            filename = backups_tree.item(selected_item, "values")[0]
            backup_file = os.path.join(backup_dir, filename)
            
            if messagebox.askyesno("تأكيد الاستعادة", f"هل أنت متأكد من استعادة النسخة الاحتياطية:\n{filename}؟\n\nسيتم استبدال البيانات الحالية!"):
                try:
                    # إنشاء نسخة احتياطية من الملف الحالي
                    if os.path.exists(self.excel_file):
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        temp_backup = f"{self.excel_file}.before_restore_{timestamp}"
                        shutil.copy2(self.excel_file, temp_backup)
                        print(f"[تم] إنشاء نسخة احتياطية قبل الاستعادة: {temp_backup}")
                    
                    # نسخ ملف النسخة الاحتياطية إلى ملف البيانات
                    shutil.copy2(backup_file, self.excel_file)
                    
                    # إعادة تحميل البيانات
                    self.load_employees_data()
                    
                    messagebox.showinfo("تم بنجاح", f"تم استعادة النسخة الاحتياطية: {filename}")
                    backups_window.destroy()
                    
                except Exception as e:
                    messagebox.showerror("خطأ", f"فشل في استعادة النسخة الاحتياطية:\n{str(e)}")
        
        # وظيفة حذف النسخة الاحتياطية
        def delete_backup():
            selected_items = backups_tree.selection()
            if not selected_items:
                messagebox.showinfo("تنبيه", "الرجاء تحديد نسخة احتياطية للحذف")
                return
            
            selected_item = selected_items[0]
            filename = backups_tree.item(selected_item, "values")[0]
            backup_file = os.path.join(backup_dir, filename)
            
            if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف النسخة الاحتياطية:\n{filename}؟"):
                try:
                    os.remove(backup_file)
                    backups_tree.delete(selected_item)
                    messagebox.showinfo("تم بنجاح", f"تم حذف النسخة الاحتياطية: {filename}")
                except Exception as e:
                    messagebox.showerror("خطأ", f"فشل في حذف النسخة الاحتياطية:\n{str(e)}")
        
        # زر الاستعادة
        restore_button = tk.Button(buttons_frame, text="استعادة النسخة المحددة", 
                                 command=restore_backup,
                                 bg="#27ae60", fg="white", font=("Arial", 12, "bold"),
                                 padx=20, pady=10)
        restore_button.pack(side=tk.LEFT, padx=10)
        
        # زر الحذف
        delete_button = tk.Button(buttons_frame, text="حذف النسخة المحددة", 
                               command=delete_backup,
                               bg="#e74c3c", fg="white", font=("Arial", 12, "bold"),
                               padx=20, pady=10)
        delete_button.pack(side=tk.LEFT, padx=10)
        
        # زر الإغلاق
        close_button = tk.Button(buttons_frame, text="إغلاق", 
                              command=backups_window.destroy,
                              bg="#3498db", fg="white", font=("Arial", 12, "bold"),
                              padx=20, pady=10)
        close_button.pack(side=tk.LEFT, padx=10)

# تشغيل النظام إذا تم تشغيل الملف مباشرة
if __name__ == "__main__":
    root = tk.Tk()
    app = EmployeeManagementSystem(root)
    root.mainloop()